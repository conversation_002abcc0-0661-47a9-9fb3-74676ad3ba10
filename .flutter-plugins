# This is a generated file; do not edit or check into version control.
app_links=/Users/<USER>/.pub-cache/hosted/pub.dev/app_links-6.4.0/
app_links_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/app_links_linux-1.0.3/
app_links_web=/Users/<USER>/.pub-cache/hosted/pub.dev/app_links_web-1.0.4/
connectivity_plus=/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-5.0.2/
file_picker=/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-10.2.0/
file_selector_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/
file_selector_macos=/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3/
file_selector_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/
firebase_analytics=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.5.2/
firebase_analytics_web=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics_web-0.5.10+15/
firebase_core=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.15.1/
firebase_core_web=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.24.1/
firebase_crashlytics=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_crashlytics-4.3.9/
firebase_performance=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_performance-0.10.1+9/
firebase_performance_web=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_performance_web-0.1.7+15/
firebase_remote_config=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_remote_config-5.4.7/
firebase_remote_config_web=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_remote_config_web-1.8.7/
flutter_local_notifications=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-16.3.3/
flutter_local_notifications_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-4.0.1/
flutter_plugin_android_lifecycle=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/
google_sign_in=/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in-6.3.0/
google_sign_in_android=/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_android-6.2.1/
google_sign_in_ios=/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/
google_sign_in_web=/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_web-0.12.4+4/
gtk=/Users/<USER>/.pub-cache/hosted/pub.dev/gtk-2.1.0/
image_picker=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker-1.1.2/
image_picker_android=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/
image_picker_for_web=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6/
image_picker_ios=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/
image_picker_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/
image_picker_macos=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/
image_picker_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/
open_file=/Users/<USER>/.pub-cache/hosted/pub.dev/open_file-3.5.10/
open_file_android=/Users/<USER>/.pub-cache/hosted/pub.dev/open_file_android-1.0.6/
open_file_ios=/Users/<USER>/.pub-cache/hosted/pub.dev/open_file_ios-1.0.3/
open_file_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/open_file_linux-0.0.5/
open_file_mac=/Users/<USER>/.pub-cache/hosted/pub.dev/open_file_mac-1.0.3/
open_file_web=/Users/<USER>/.pub-cache/hosted/pub.dev/open_file_web-0.0.4/
open_file_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/open_file_windows-0.0.3/
package_info_plus=/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/
path_provider=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/
path_provider_android=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/
path_provider_foundation=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/
path_provider_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/
path_provider_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/
permission_handler=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-11.4.0/
permission_handler_android=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-12.1.0/
permission_handler_apple=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/
permission_handler_html=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/
permission_handler_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/
purchases_flutter=/Users/<USER>/.pub-cache/hosted/pub.dev/purchases_flutter-8.10.5/
purchases_ui_flutter=/Users/<USER>/.pub-cache/hosted/pub.dev/purchases_ui_flutter-8.10.5/
share_plus=/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-7.2.2/
shared_preferences=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/
shared_preferences_android=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/
shared_preferences_foundation=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/
shared_preferences_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/
shared_preferences_web=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/
shared_preferences_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/
sign_in_with_apple=/Users/<USER>/.pub-cache/hosted/pub.dev/sign_in_with_apple-7.0.1/
sign_in_with_apple_web=/Users/<USER>/.pub-cache/hosted/pub.dev/sign_in_with_apple_web-3.0.0/
sqlite3_flutter_libs=/Users/<USER>/.pub-cache/hosted/pub.dev/sqlite3_flutter_libs-0.5.34/
url_launcher=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/
url_launcher_android=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/
url_launcher_ios=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/
url_launcher_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/
url_launcher_macos=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/
url_launcher_web=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1/
url_launcher_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/
