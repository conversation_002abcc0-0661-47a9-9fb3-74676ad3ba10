_fe_analyzer_shared
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/_fe_analyzer_shared-76.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/_fe_analyzer_shared-76.0.0/lib/
_flutterfire_internals
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/_flutterfire_internals-1.3.58/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/_flutterfire_internals-1.3.58/lib/
analyzer
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/analyzer-6.11.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/analyzer-6.11.0/lib/
app_links
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/app_links-6.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/app_links-6.4.0/lib/
app_links_linux
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/app_links_linux-1.0.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/app_links_linux-1.0.3/lib/
app_links_platform_interface
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/app_links_platform_interface-2.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/app_links_platform_interface-2.0.2/lib/
app_links_web
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/app_links_web-1.0.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/app_links_web-1.0.4/lib/
archive
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/
args
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/args-2.7.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/args-2.7.0/lib/
async
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/
bloc
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/bloc-8.1.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/bloc-8.1.4/lib/
boolean_selector
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/lib/
build
3.7
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build-2.5.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build-2.5.4/lib/
build_config
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_config-1.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_config-1.1.2/lib/
build_daemon
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_daemon-4.0.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_daemon-4.0.4/lib/
build_resolvers
3.7
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_resolvers-2.5.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_resolvers-2.5.4/lib/
build_runner
3.7
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_runner-2.5.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_runner-2.5.4/lib/
build_runner_core
3.7
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_runner_core-9.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_runner_core-9.1.2/lib/
built_collection
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/built_collection-5.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/built_collection-5.1.1/lib/
built_value
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/built_value-8.10.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/built_value-8.10.1/lib/
characters
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/
charcode
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/charcode-1.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/charcode-1.4.0/lib/
checked_yaml
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.3/lib/
cli_util
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cli_util-0.4.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cli_util-0.4.2/lib/
clock
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/
code_builder
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/code_builder-4.10.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/code_builder-4.10.1/lib/
collection
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/
connectivity_plus
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-5.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-5.0.2/lib/
connectivity_plus_platform_interface
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-1.2.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-1.2.4/lib/
convert
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/convert-3.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/convert-3.1.2/lib/
cross_file
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/lib/
crypto
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/
cupertino_icons
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/lib/
dart_style
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dart_style-2.3.8/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dart_style-2.3.8/lib/
dbus
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/
dio
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/
dio_web_adapter
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dio_web_adapter-2.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dio_web_adapter-2.1.1/lib/
drift
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/drift-2.26.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/drift-2.26.1/lib/
drift_dev
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/drift_dev-2.26.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/drift_dev-2.26.0/lib/
equatable
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/lib/
fake_async
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.2/lib/
ffi
3.7
file:///Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/
figma_squircle
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/figma_squircle-0.5.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/figma_squircle-0.5.3/lib/
file
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/
file_picker
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-10.2.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-10.2.0/lib/
file_selector_linux
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/lib/
file_selector_macos
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3/lib/
file_selector_platform_interface
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/
file_selector_windows
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/lib/
firebase_analytics
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.5.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.5.2/lib/
firebase_analytics_platform_interface
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics_platform_interface-4.4.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics_platform_interface-4.4.2/lib/
firebase_analytics_web
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics_web-0.5.10+15/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics_web-0.5.10+15/lib/
firebase_core
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.15.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.15.1/lib/
firebase_core_platform_interface
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-6.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-6.0.0/lib/
firebase_core_web
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.24.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.24.1/lib/
firebase_crashlytics
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_crashlytics-4.3.9/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_crashlytics-4.3.9/lib/
firebase_crashlytics_platform_interface
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_crashlytics_platform_interface-3.8.9/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_crashlytics_platform_interface-3.8.9/lib/
firebase_performance
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_performance-0.10.1+9/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_performance-0.10.1+9/lib/
firebase_performance_platform_interface
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_performance_platform_interface-0.1.5+9/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_performance_platform_interface-0.1.5+9/lib/
firebase_performance_web
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_performance_web-0.1.7+15/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_performance_web-0.1.7+15/lib/
firebase_remote_config
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_remote_config-5.4.7/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_remote_config-5.4.7/lib/
firebase_remote_config_platform_interface
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_remote_config_platform_interface-1.5.7/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_remote_config_platform_interface-1.5.7/lib/
firebase_remote_config_web
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_remote_config_web-1.8.7/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_remote_config_web-1.8.7/lib/
fixnum
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/
fl_chart
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-1.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-1.0.0/lib/
flutter_bloc
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_bloc-8.1.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_bloc-8.1.6/lib/
flutter_launcher_icons
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_launcher_icons-0.13.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_launcher_icons-0.13.1/lib/
flutter_lints
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-5.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-5.0.0/lib/
flutter_local_notifications
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-16.3.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-16.3.3/lib/
flutter_local_notifications_linux
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-4.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-4.0.1/lib/
flutter_local_notifications_platform_interface
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_platform_interface-7.2.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_platform_interface-7.2.0/lib/
flutter_plugin_android_lifecycle
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/lib/
flutter_svg
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_svg-2.2.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_svg-2.2.0/lib/
font_awesome_flutter
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/font_awesome_flutter-10.8.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/font_awesome_flutter-10.8.0/lib/
freezed_annotation
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/freezed_annotation-2.4.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/freezed_annotation-2.4.4/lib/
frontend_server_client
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/frontend_server_client-4.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/frontend_server_client-4.0.0/lib/
functions_client
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/functions_client-2.4.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/functions_client-2.4.3/lib/
get_it
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/get_it-7.7.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/get_it-7.7.0/lib/
glob
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/glob-2.1.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/glob-2.1.3/lib/
go_router
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/go_router-15.1.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/go_router-15.1.3/lib/
google_identity_services_web
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_identity_services_web-0.3.3+1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_identity_services_web-0.3.3+1/lib/
google_sign_in
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in-6.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in-6.3.0/lib/
google_sign_in_android
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_android-6.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_android-6.2.1/lib/
google_sign_in_ios
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/lib/
google_sign_in_platform_interface
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_platform_interface-2.5.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_platform_interface-2.5.0/lib/
google_sign_in_web
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_web-0.12.4+4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_web-0.12.4+4/lib/
gotrue
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/gotrue-2.13.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/gotrue-2.13.0/lib/
graphs
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/graphs-2.3.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/graphs-2.3.2/lib/
gtk
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/gtk-2.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/gtk-2.1.0/lib/
http
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/
http_multi_server
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_multi_server-3.2.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_multi_server-3.2.2/lib/
http_parser
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/
image
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/
image_picker
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker-1.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker-1.1.2/lib/
image_picker_android
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/lib/
image_picker_for_web
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6/lib/
image_picker_ios
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/lib/
image_picker_linux
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/lib/
image_picker_macos
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/lib/
image_picker_platform_interface
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/
image_picker_windows
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/lib/
intl
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/
intl_translation
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/intl_translation-0.20.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/intl_translation-0.20.1/lib/
io
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/io-1.0.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/io-1.0.5/lib/
js
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/js-0.6.7/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/js-0.6.7/lib/
json_annotation
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/
jwt_decode
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/jwt_decode-0.3.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/jwt_decode-0.3.1/lib/
leak_tracker
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.8/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.8/lib/
leak_tracker_flutter_testing
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/lib/
leak_tracker_testing
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/lib/
lints
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/lints-5.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/lints-5.1.1/lib/
logging
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/lib/
macros
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/macros-0.1.3-main.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/macros-0.1.3-main.0/lib/
matcher
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17/lib/
material_color_utilities
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/
meta
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/lib/
mime
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/mime-1.0.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/mime-1.0.6/lib/
nested
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0/lib/
nm
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/nm-0.5.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/nm-0.5.0/lib/
open_file
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/open_file-3.5.10/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/open_file-3.5.10/lib/
open_file_android
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/open_file_android-1.0.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/open_file_android-1.0.6/lib/
open_file_ios
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/open_file_ios-1.0.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/open_file_ios-1.0.3/lib/
open_file_linux
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/open_file_linux-0.0.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/open_file_linux-0.0.5/lib/
open_file_mac
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/open_file_mac-1.0.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/open_file_mac-1.0.3/lib/
open_file_platform_interface
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/open_file_platform_interface-1.0.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/open_file_platform_interface-1.0.3/lib/
open_file_web
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/open_file_web-0.0.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/open_file_web-0.0.4/lib/
open_file_windows
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/open_file_windows-0.0.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/open_file_windows-0.0.3/lib/
package_config
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_config-2.2.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_config-2.2.0/lib/
package_info_plus
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/lib/
package_info_plus_platform_interface
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus_platform_interface-3.2.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus_platform_interface-3.2.0/lib/
path
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/
path_parsing
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_parsing-1.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_parsing-1.1.0/lib/
path_provider
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/lib/
path_provider_android
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/lib/
path_provider_foundation
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/lib/
path_provider_linux
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/
path_provider_platform_interface
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/
path_provider_windows
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/
permission_handler
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-11.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-11.4.0/lib/
permission_handler_android
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-12.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-12.1.0/lib/
permission_handler_apple
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/lib/
permission_handler_html
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/lib/
permission_handler_platform_interface
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/
permission_handler_windows
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/lib/
petitparser
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/
platform
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/
plugin_platform_interface
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/lib/
pool
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pool-1.5.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pool-1.5.1/lib/
posix
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/posix-6.0.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/posix-6.0.3/lib/
postgrest
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/postgrest-2.4.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/postgrest-2.4.2/lib/
provider
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/
pub_semver
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pub_semver-2.2.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pub_semver-2.2.0/lib/
pubspec_parse
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pubspec_parse-1.5.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pubspec_parse-1.5.0/lib/
purchases_flutter
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/purchases_flutter-8.10.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/purchases_flutter-8.10.5/lib/
purchases_ui_flutter
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/purchases_ui_flutter-8.10.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/purchases_ui_flutter-8.10.5/lib/
realtime_client
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/realtime_client-2.5.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/realtime_client-2.5.1/lib/
recase
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/recase-4.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/recase-4.1.0/lib/
retry
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/retry-3.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/retry-3.1.2/lib/
rxdart
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.27.7/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.27.7/lib/
share_plus
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-7.2.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-7.2.2/lib/
share_plus_platform_interface
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/share_plus_platform_interface-3.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/share_plus_platform_interface-3.4.0/lib/
shared_preferences
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/lib/
shared_preferences_android
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/lib/
shared_preferences_foundation
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/
shared_preferences_linux
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/lib/
shared_preferences_platform_interface
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/
shared_preferences_web
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/lib/
shared_preferences_windows
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/lib/
shelf
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf-1.4.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf-1.4.2/lib/
shelf_web_socket
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf_web_socket-3.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf_web_socket-3.0.0/lib/
sign_in_with_apple
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sign_in_with_apple-7.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sign_in_with_apple-7.0.1/lib/
sign_in_with_apple_platform_interface
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sign_in_with_apple_platform_interface-2.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sign_in_with_apple_platform_interface-2.0.0/lib/
sign_in_with_apple_web
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sign_in_with_apple_web-3.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sign_in_with_apple_web-3.0.0/lib/
source_gen
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_gen-2.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_gen-2.0.0/lib/
source_span
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/
sprintf
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/
sqlite3
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqlite3-2.7.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqlite3-2.7.6/lib/
sqlite3_flutter_libs
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqlite3_flutter_libs-0.5.34/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqlite3_flutter_libs-0.5.34/lib/
sqlparser
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqlparser-0.41.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqlparser-0.41.0/lib/
stack_trace
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/
storage_client
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/storage_client-2.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/storage_client-2.4.0/lib/
stream_channel
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/lib/
stream_transform
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/
string_scanner
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/
supabase
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/supabase-2.8.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/supabase-2.8.0/lib/
supabase_flutter
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/supabase_flutter-2.9.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/supabase_flutter-2.9.1/lib/
term_glyph
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/
test_api
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4/lib/
timezone
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/timezone-0.9.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/timezone-0.9.4/lib/
timing
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/timing-1.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/timing-1.0.2/lib/
typed_data
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/
url_launcher
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/lib/
url_launcher_android
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/lib/
url_launcher_ios
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/lib/
url_launcher_linux
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/lib/
url_launcher_macos
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/lib/
url_launcher_platform_interface
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/
url_launcher_web
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1/lib/
url_launcher_windows
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/lib/
uuid
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/
vector_graphics
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics-1.1.19/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics-1.1.19/lib/
vector_graphics_codec
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_codec-1.1.13/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_codec-1.1.13/lib/
vector_graphics_compiler
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.17/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.17/lib/
vector_math
2.14
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/
vm_service
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-14.3.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-14.3.1/lib/
watcher
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/watcher-1.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/watcher-1.1.2/lib/
web
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/
web_socket
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web_socket-1.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web_socket-1.0.1/lib/
web_socket_channel
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-3.0.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-3.0.3/lib/
win32
3.7
file:///Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/
xdg_directories
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/lib/
xml
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/
yaml
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.3/lib/
yet_another_json_isolate
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/yet_another_json_isolate-2.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/yet_another_json_isolate-2.1.0/lib/
ailacarte
3.7
file:///Users/<USER>/Development/ailacarte/
file:///Users/<USER>/Development/ailacarte/lib/
_macros
3.4
file:///Users/<USER>/Development/flutter/bin/cache/dart-sdk/pkg/_macros/
file:///Users/<USER>/Development/flutter/bin/cache/dart-sdk/pkg/_macros/lib/
sky_engine
3.7
file:///Users/<USER>/Development/flutter/bin/cache/pkg/sky_engine/
file:///Users/<USER>/Development/flutter/bin/cache/pkg/sky_engine/lib/
flutter
3.7
file:///Users/<USER>/Development/flutter/packages/flutter/
file:///Users/<USER>/Development/flutter/packages/flutter/lib/
flutter_localizations
3.7
file:///Users/<USER>/Development/flutter/packages/flutter_localizations/
file:///Users/<USER>/Development/flutter/packages/flutter_localizations/lib/
flutter_test
3.7
file:///Users/<USER>/Development/flutter/packages/flutter_test/
file:///Users/<USER>/Development/flutter/packages/flutter_test/lib/
flutter_web_plugins
3.7
file:///Users/<USER>/Development/flutter/packages/flutter_web_plugins/
file:///Users/<USER>/Development/flutter/packages/flutter_web_plugins/lib/
2
