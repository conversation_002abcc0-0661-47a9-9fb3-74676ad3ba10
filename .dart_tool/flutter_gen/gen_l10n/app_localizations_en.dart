// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'AI La Carte';

  @override
  String get welcome => 'Welcome';

  @override
  String get welcomeToApp => 'Welcome to AI La Carte';

  @override
  String get welcomeMessage => 'Your AI-powered recipe collection companion';

  @override
  String get selectLanguage => 'Select Language';

  @override
  String get getStarted => 'Get Started';

  @override
  String get next => 'Next';

  @override
  String get skip => 'Skip';

  @override
  String get done => 'Done';

  @override
  String get themeSelection => 'Choose Your Theme';

  @override
  String get lightTheme => 'Light';

  @override
  String get darkTheme => 'Dark';

  @override
  String get systemTheme => 'System';

  @override
  String get notifications => 'Notifications';

  @override
  String get notificationPermission => 'Enable notifications to get recipe reminders and updates';

  @override
  String get enableNotifications => 'Enable Notifications';

  @override
  String get recipes => 'Recipes';

  @override
  String get groceryList => 'Grocery List';

  @override
  String get settings => 'Settings';

  @override
  String get account => 'Account';

  @override
  String get login => 'Login';

  @override
  String get logout => 'Logout';

  @override
  String get email => 'Email';

  @override
  String get password => 'Password';

  @override
  String get splashCheckingAuth => 'Checking authentication...';

  @override
  String get splashSyncingData => 'Syncing data...';

  @override
  String get splashErrorTitle => 'Initialization Error';

  @override
  String get splashRetryButton => 'Retry';
}
