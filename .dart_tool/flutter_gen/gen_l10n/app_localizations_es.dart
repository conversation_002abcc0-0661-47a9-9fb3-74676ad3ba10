// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Spanish Castilian (`es`).
class AppLocalizationsEs extends AppLocalizations {
  AppLocalizationsEs([String locale = 'es']) : super(locale);

  @override
  String get appTitle => 'AI La Carte';

  @override
  String get welcome => 'Bienvenido';

  @override
  String get welcomeToApp => 'Bienvenido a AI La Carte';

  @override
  String get welcomeMessage => 'Tu compañero de recetas impulsado por IA';

  @override
  String get selectLanguage => 'Seleccionar idioma';

  @override
  String get getStarted => 'Comenzar';

  @override
  String get next => 'Siguiente';

  @override
  String get skip => 'Omitir';

  @override
  String get done => 'Hecho';

  @override
  String get themeSelection => 'Elige tu tema';

  @override
  String get lightTheme => 'Claro';

  @override
  String get darkTheme => 'Oscuro';

  @override
  String get systemTheme => 'Sistema';

  @override
  String get notifications => 'Notificaciones';

  @override
  String get notificationPermission => 'Habilita las notificaciones para recibir recordatorios de recetas y actualizaciones';

  @override
  String get enableNotifications => 'Habilitar notificaciones';

  @override
  String get recipes => 'Recetas';

  @override
  String get groceryList => 'Lista de compras';

  @override
  String get settings => 'Configuración';

  @override
  String get account => 'Cuenta';

  @override
  String get login => 'Iniciar sesión';

  @override
  String get logout => 'Cerrar sesión';

  @override
  String get email => 'Correo electrónico';

  @override
  String get password => 'Contraseña';

  @override
  String get splashCheckingAuth => 'Verificando autenticación...';

  @override
  String get splashSyncingData => 'Sincronizando datos...';

  @override
  String get splashErrorTitle => 'Error de inicialización';

  @override
  String get splashRetryButton => 'Reintentar';
}
