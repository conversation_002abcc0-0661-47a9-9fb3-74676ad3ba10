// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for German (`de`).
class AppLocalizationsDe extends AppLocalizations {
  AppLocalizationsDe([String locale = 'de']) : super(locale);

  @override
  String get appTitle => 'AI La Carte';

  @override
  String get welcome => 'Willkommen';

  @override
  String get welcomeToApp => 'Willkommen bei AI La Carte';

  @override
  String get welcomeMessage => 'Ihr KI-gestützter Rezept-Begleiter';

  @override
  String get selectLanguage => 'Sprache auswählen';

  @override
  String get getStarted => 'Loslegen';

  @override
  String get next => 'Weiter';

  @override
  String get skip => 'Überspringen';

  @override
  String get done => 'Fertig';

  @override
  String get themeSelection => 'Wählen Sie Ihr Design';

  @override
  String get lightTheme => 'Hell';

  @override
  String get darkTheme => 'Dunkel';

  @override
  String get systemTheme => 'System';

  @override
  String get notifications => 'Benachrichtigungen';

  @override
  String get notificationPermission => 'Aktivieren Sie Benachrichtigungen für Rezept-Erinnerungen und Updates';

  @override
  String get enableNotifications => 'Benachrichtigungen aktivieren';

  @override
  String get recipes => 'Rezepte';

  @override
  String get groceryList => 'Einkaufsliste';

  @override
  String get settings => 'Einstellungen';

  @override
  String get account => 'Konto';

  @override
  String get login => 'Anmelden';

  @override
  String get logout => 'Abmelden';

  @override
  String get email => 'E-Mail';

  @override
  String get password => 'Passwort';

  @override
  String get splashCheckingAuth => 'Authentifizierung prüfen...';

  @override
  String get splashSyncingData => 'Daten synchronisieren...';

  @override
  String get splashErrorTitle => 'Initialisierungsfehler';

  @override
  String get splashRetryButton => 'Wiederholen';
}
