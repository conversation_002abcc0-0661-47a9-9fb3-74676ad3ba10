// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for French (`fr`).
class AppLocalizationsFr extends AppLocalizations {
  AppLocalizationsFr([String locale = 'fr']) : super(locale);

  @override
  String get appTitle => 'AI La Carte';

  @override
  String get welcome => 'Bienvenue';

  @override
  String get welcomeToApp => 'Bienvenue dans AI La Carte';

  @override
  String get welcomeMessage => 'Votre compagnon de recettes alimenté par l\'IA';

  @override
  String get selectLanguage => 'Sélectionner la langue';

  @override
  String get getStarted => 'Commencer';

  @override
  String get next => 'Suivant';

  @override
  String get skip => 'Ignorer';

  @override
  String get done => 'Terminé';

  @override
  String get themeSelection => 'Choisissez votre thème';

  @override
  String get lightTheme => 'Clair';

  @override
  String get darkTheme => 'Sombre';

  @override
  String get systemTheme => 'Système';

  @override
  String get notifications => 'Notifications';

  @override
  String get notificationPermission => 'Activez les notifications pour recevoir des rappels de recettes et des mises à jour';

  @override
  String get enableNotifications => 'Activer les notifications';

  @override
  String get recipes => 'Recettes';

  @override
  String get groceryList => 'Liste de courses';

  @override
  String get settings => 'Paramètres';

  @override
  String get account => 'Compte';

  @override
  String get login => 'Se connecter';

  @override
  String get logout => 'Se déconnecter';

  @override
  String get email => 'E-mail';

  @override
  String get password => 'Mot de passe';

  @override
  String get splashCheckingAuth => 'Vérification de l\'authentification...';

  @override
  String get splashSyncingData => 'Synchronisation des données...';

  @override
  String get splashErrorTitle => 'Erreur d\'initialisation';

  @override
  String get splashRetryButton => 'Réessayer';
}
