{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "app_links", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/app_links-6.4.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-5.0.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "file_picker", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-10.2.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_analytics", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.5.2/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.15.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_crashlytics", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_crashlytics-4.3.9/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_performance", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_performance-0.10.1+9/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_remote_config", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_remote_config-5.4.7/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "flutter_local_notifications", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-16.3.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "google_sign_in_ios", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "image_picker_ios", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "open_file_ios", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/open_file_ios-1.0.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_apple", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "purchases_flutter", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/purchases_flutter-8.10.5/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "purchases_ui_flutter", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/purchases_ui_flutter-8.10.5/", "native_build": true, "dependencies": ["purchases_flutter"], "dev_dependency": false}, {"name": "share_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-7.2.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sign_in_with_apple", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sign_in_with_apple-7.0.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqlite3_flutter_libs", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqlite3_flutter_libs-0.5.34/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_ios", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/", "native_build": true, "dependencies": [], "dev_dependency": false}], "android": [{"name": "app_links", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/app_links-6.4.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-5.0.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "file_picker", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-10.2.0/", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"], "dev_dependency": false}, {"name": "firebase_analytics", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.5.2/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.15.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_crashlytics", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_crashlytics-4.3.9/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_performance", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_performance-0.10.1+9/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_remote_config", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_remote_config-5.4.7/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "flutter_local_notifications", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-16.3.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_plugin_android_lifecycle", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "google_sign_in_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_android-6.2.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "image_picker_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"], "dev_dependency": false}, {"name": "open_file_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/open_file_android-1.0.6/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-12.1.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "purchases_flutter", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/purchases_flutter-8.10.5/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "purchases_ui_flutter", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/purchases_ui_flutter-8.10.5/", "native_build": true, "dependencies": ["purchases_flutter"], "dev_dependency": false}, {"name": "share_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-7.2.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sign_in_with_apple", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sign_in_with_apple-7.0.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqlite3_flutter_libs", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqlite3_flutter_libs-0.5.34/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/", "native_build": true, "dependencies": [], "dev_dependency": false}], "macos": [{"name": "app_links", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/app_links-6.4.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-5.0.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "file_picker", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-10.2.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "file_selector_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_analytics", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.5.2/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.15.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_crashlytics", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_crashlytics-4.3.9/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_remote_config", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_remote_config-5.4.7/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "flutter_local_notifications", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-16.3.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "google_sign_in_ios", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "image_picker_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/", "native_build": false, "dependencies": ["file_selector_macos"], "dev_dependency": false}, {"name": "open_file_mac", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/open_file_mac-1.0.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "purchases_flutter", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/purchases_flutter-8.10.5/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "share_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-7.2.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sign_in_with_apple", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sign_in_with_apple-7.0.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqlite3_flutter_libs", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqlite3_flutter_libs-0.5.34/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/", "native_build": true, "dependencies": [], "dev_dependency": false}], "linux": [{"name": "app_links_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/app_links_linux-1.0.3/", "native_build": false, "dependencies": ["gtk"], "dev_dependency": false}, {"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-5.0.2/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "file_picker", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-10.2.0/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "file_selector_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_local_notifications_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-4.0.1/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "gtk", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gtk-2.1.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "image_picker_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/", "native_build": false, "dependencies": ["file_selector_linux"], "dev_dependency": false}, {"name": "open_file_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/open_file_linux-0.0.5/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "share_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-7.2.2/", "native_build": false, "dependencies": ["url_launcher_linux"], "dev_dependency": false}, {"name": "shared_preferences_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/", "native_build": false, "dependencies": ["path_provider_linux"], "dev_dependency": false}, {"name": "sqlite3_flutter_libs", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqlite3_flutter_libs-0.5.34/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/", "native_build": true, "dependencies": [], "dev_dependency": false}], "windows": [{"name": "app_links", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/app_links-6.4.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-5.0.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "file_picker", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-10.2.0/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "file_selector_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.15.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "image_picker_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/", "native_build": false, "dependencies": ["file_selector_windows"], "dev_dependency": false}, {"name": "open_file_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/open_file_windows-0.0.3/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "share_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-7.2.2/", "native_build": true, "dependencies": ["url_launcher_windows"], "dev_dependency": false}, {"name": "shared_preferences_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/", "native_build": false, "dependencies": ["path_provider_windows"], "dev_dependency": false}, {"name": "sqlite3_flutter_libs", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqlite3_flutter_libs-0.5.34/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/", "native_build": true, "dependencies": [], "dev_dependency": false}], "web": [{"name": "app_links_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/app_links_web-1.0.4/", "dependencies": [], "dev_dependency": false}, {"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-5.0.2/", "dependencies": [], "dev_dependency": false}, {"name": "file_picker", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-10.2.0/", "dependencies": [], "dev_dependency": false}, {"name": "firebase_analytics_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics_web-0.5.10+15/", "dependencies": ["firebase_core_web"], "dev_dependency": false}, {"name": "firebase_core_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.24.1/", "dependencies": [], "dev_dependency": false}, {"name": "firebase_performance_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_performance_web-0.1.7+15/", "dependencies": ["firebase_core_web"], "dev_dependency": false}, {"name": "firebase_remote_config_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_remote_config_web-1.8.7/", "dependencies": ["firebase_core_web"], "dev_dependency": false}, {"name": "google_sign_in_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_web-0.12.4+4/", "dependencies": [], "dev_dependency": false}, {"name": "image_picker_for_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6/", "dependencies": [], "dev_dependency": false}, {"name": "open_file_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/open_file_web-0.0.4/", "dependencies": [], "dev_dependency": false}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/", "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_html", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/", "dependencies": [], "dev_dependency": false}, {"name": "share_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-7.2.2/", "dependencies": ["url_launcher_web"], "dev_dependency": false}, {"name": "shared_preferences_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/", "dependencies": [], "dev_dependency": false}, {"name": "sign_in_with_apple_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sign_in_with_apple_web-3.0.0/", "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1/", "dependencies": [], "dev_dependency": false}]}, "dependencyGraph": [{"name": "app_links", "dependencies": ["app_links_linux", "app_links_web"]}, {"name": "app_links_linux", "dependencies": ["gtk"]}, {"name": "app_links_web", "dependencies": []}, {"name": "connectivity_plus", "dependencies": []}, {"name": "file_picker", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "file_selector_linux", "dependencies": []}, {"name": "file_selector_macos", "dependencies": []}, {"name": "file_selector_windows", "dependencies": []}, {"name": "firebase_analytics", "dependencies": ["firebase_analytics_web", "firebase_core"]}, {"name": "firebase_analytics_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "firebase_core", "dependencies": ["firebase_core_web"]}, {"name": "firebase_core_web", "dependencies": []}, {"name": "firebase_crashlytics", "dependencies": ["firebase_core"]}, {"name": "firebase_performance", "dependencies": ["firebase_core", "firebase_performance_web"]}, {"name": "firebase_performance_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "firebase_remote_config", "dependencies": ["firebase_core", "firebase_remote_config_web"]}, {"name": "firebase_remote_config_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "flutter_local_notifications", "dependencies": ["flutter_local_notifications_linux"]}, {"name": "flutter_local_notifications_linux", "dependencies": []}, {"name": "flutter_plugin_android_lifecycle", "dependencies": []}, {"name": "google_sign_in", "dependencies": ["google_sign_in_android", "google_sign_in_ios", "google_sign_in_web"]}, {"name": "google_sign_in_android", "dependencies": []}, {"name": "google_sign_in_ios", "dependencies": []}, {"name": "google_sign_in_web", "dependencies": []}, {"name": "gtk", "dependencies": []}, {"name": "image_picker", "dependencies": ["image_picker_android", "image_picker_for_web", "image_picker_ios", "image_picker_linux", "image_picker_macos", "image_picker_windows"]}, {"name": "image_picker_android", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "image_picker_for_web", "dependencies": []}, {"name": "image_picker_ios", "dependencies": []}, {"name": "image_picker_linux", "dependencies": ["file_selector_linux"]}, {"name": "image_picker_macos", "dependencies": ["file_selector_macos"]}, {"name": "image_picker_windows", "dependencies": ["file_selector_windows"]}, {"name": "open_file", "dependencies": ["open_file_android", "open_file_web", "open_file_ios", "open_file_mac", "open_file_windows", "open_file_linux"]}, {"name": "open_file_android", "dependencies": []}, {"name": "open_file_ios", "dependencies": []}, {"name": "open_file_linux", "dependencies": []}, {"name": "open_file_mac", "dependencies": []}, {"name": "open_file_web", "dependencies": []}, {"name": "open_file_windows", "dependencies": []}, {"name": "package_info_plus", "dependencies": []}, {"name": "path_provider", "dependencies": ["path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_windows"]}, {"name": "path_provider_android", "dependencies": []}, {"name": "path_provider_foundation", "dependencies": []}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "permission_handler", "dependencies": ["permission_handler_android", "permission_handler_apple", "permission_handler_html", "permission_handler_windows"]}, {"name": "permission_handler_android", "dependencies": []}, {"name": "permission_handler_apple", "dependencies": []}, {"name": "permission_handler_html", "dependencies": []}, {"name": "permission_handler_windows", "dependencies": []}, {"name": "purchases_flutter", "dependencies": []}, {"name": "purchases_ui_flutter", "dependencies": ["purchases_flutter"]}, {"name": "share_plus", "dependencies": ["url_launcher_web", "url_launcher_windows", "url_launcher_linux"]}, {"name": "shared_preferences", "dependencies": ["shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "shared_preferences_android", "dependencies": []}, {"name": "shared_preferences_foundation", "dependencies": []}, {"name": "shared_preferences_linux", "dependencies": ["path_provider_linux"]}, {"name": "shared_preferences_web", "dependencies": []}, {"name": "shared_preferences_windows", "dependencies": ["path_provider_windows"]}, {"name": "sign_in_with_apple", "dependencies": ["sign_in_with_apple_web"]}, {"name": "sign_in_with_apple_web", "dependencies": []}, {"name": "sqlite3_flutter_libs", "dependencies": []}, {"name": "url_launcher", "dependencies": ["url_launcher_android", "url_launcher_ios", "url_launcher_linux", "url_launcher_macos", "url_launcher_web", "url_launcher_windows"]}, {"name": "url_launcher_android", "dependencies": []}, {"name": "url_launcher_ios", "dependencies": []}, {"name": "url_launcher_linux", "dependencies": []}, {"name": "url_launcher_macos", "dependencies": []}, {"name": "url_launcher_web", "dependencies": []}, {"name": "url_launcher_windows", "dependencies": []}], "date_created": "2025-07-08 20:20:47.725476", "version": "3.29.2", "swift_package_manager_enabled": {"ios": false, "macos": false}}