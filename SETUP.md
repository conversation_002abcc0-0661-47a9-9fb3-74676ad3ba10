# AI La Carte Setup Guide

This guide will help you set up the AI La Carte app with all necessary configurations.

## Prerequisites

- Flutter SDK (3.7.2 or higher)
- Dart SDK
- iOS development tools (Xcode)
- Firebase account
- Supabase account
- RevenueCat account

## Step 1: Firebase Setup

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Create a new project or use existing one
3. Add iOS app with bundle ID: `com.pankaj6apr.ailacarte`
4. Download `GoogleService-Info.plist` and place it in `ios/Runner/`
5. Run the FlutterFire CLI to generate Firebase options:
   ```bash
   flutter pub global activate flutterfire_cli
   flutterfire configure
   ```
6. This will update `lib/firebase_options.dart` with your project's configuration

## Step 2: Supabase Setup

1. Go to [Supabase Dashboard](https://supabase.com/dashboard)
2. Create a new project
3. Go to Settings > API
4. Copy your project URL and anon key
5. Update `lib/utils/api_keys.dart`:
   ```dart
   static const String supabaseUrl = 'YOUR_SUPABASE_URL';
   static const String supabaseAnonKey = 'YOUR_SUPABASE_ANON_KEY';
   ```

### Supabase Database Schema

Create the following tables in your Supabase database:

```sql
-- Recipes table
CREATE TABLE recipes (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT,
  ingredients TEXT[] NOT NULL,
  instructions TEXT[] NOT NULL,
  cooking_time_minutes INTEGER NOT NULL,
  servings INTEGER NOT NULL,
  difficulty TEXT NOT NULL CHECK (difficulty IN ('Easy', 'Medium', 'Hard')),
  category TEXT NOT NULL,
  image_url TEXT,
  tags TEXT[] DEFAULT '{}',
  is_favorite BOOLEAN DEFAULT FALSE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Grocery items table
CREATE TABLE grocery_items (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  quantity TEXT NOT NULL,
  category TEXT NOT NULL,
  is_completed BOOLEAN DEFAULT FALSE,
  notes TEXT,
  recipe_id UUID REFERENCES recipes(id) ON DELETE SET NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE recipes ENABLE ROW LEVEL SECURITY;
ALTER TABLE grocery_items ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can view their own recipes" ON recipes
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own recipes" ON recipes
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own recipes" ON recipes
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own recipes" ON recipes
  FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Users can view their own grocery items" ON grocery_items
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own grocery items" ON grocery_items
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own grocery items" ON grocery_items
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own grocery items" ON grocery_items
  FOR DELETE USING (auth.uid() = user_id);
```

## Step 3: RevenueCat Setup

1. Go to [RevenueCat Dashboard](https://app.revenuecat.com/)
2. Create a new project
3. Add your iOS app with bundle ID: `com.pankaj6apr.ailacarte`
4. Get your API keys from the API Keys section
5. Update `lib/utils/api_keys.dart`:
   ```dart
   static const String revenueCatIosKey = 'YOUR_REVENUECAT_IOS_KEY';
   static const String revenueCatAndroidKey = 'YOUR_REVENUECAT_ANDROID_KEY';
   ```

## Step 4: Google Sign-In Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create OAuth 2.0 credentials
3. Add your iOS bundle ID
4. Update `lib/utils/api_keys.dart`:
   ```dart
   static const String googleIosClientId = 'YOUR_GOOGLE_IOS_CLIENT_ID';
   static const String googleServerClientId = 'YOUR_GOOGLE_SERVER_CLIENT_ID';
   ```

## Step 5: iOS Configuration

1. Open `ios/Runner.xcworkspace` in Xcode
2. Add the following URL schemes in Info.plist:
   ```xml
   <key>CFBundleURLTypes</key>
   <array>
     <dict>
       <key>CFBundleURLName</key>
       <string>com.pankaj6apr.ailacarte</string>
       <key>CFBundleURLSchemes</key>
       <array>
         <string>com.pankaj6apr.ailacarte</string>
       </array>
     </dict>
   </array>
   ```

## Step 6: Install Dependencies

```bash
flutter pub get
flutter pub run build_runner build
```

## Step 7: Run the App

```bash
flutter run
```

## Troubleshooting

### Common Issues

1. **Firebase not initialized**: Make sure `GoogleService-Info.plist` is in the correct location
2. **Supabase connection failed**: Check your URL and API key
3. **RevenueCat initialization failed**: Verify your API keys
4. **Build errors**: Run `flutter clean && flutter pub get`

### Getting Help

- Check the [Flutter documentation](https://flutter.dev/docs)
- Review [Firebase Flutter setup](https://firebase.flutter.dev/docs/overview)
- Read [Supabase Flutter guide](https://supabase.com/docs/guides/getting-started/quickstarts/flutter)
- See [RevenueCat Flutter documentation](https://docs.revenuecat.com/docs/flutter)

## Security Notes

- Never commit API keys to version control
- Use environment variables for production builds
- Enable Row Level Security in Supabase
- Configure proper Firebase security rules
