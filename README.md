# AI La Carte

AI La Carte is your AI-powered recipe collection app. Discover, organize, and cook with personalized recipes tailored to your taste preferences and dietary needs.

## Features

- **AI-Powered Recipes**: Get personalized recipe recommendations
- **Smart Grocery Lists**: Automatically generate shopping lists from recipes
- **Recipe Collection**: Save and organize your favorite recipes
- **Multi-language Support**: Available in English, Spanish, French, and German
- **Dark/Light Theme**: Choose your preferred appearance
- **Cross-platform**: iOS and Android support

## Getting Started

### Prerequisites

- Flutter SDK (3.7.2 or higher)
- Dart SDK
- iOS development tools (for iOS)
- Android development tools (for Android)

### Setup

1. Clone the repository
2. Install dependencies:
   ```bash
   flutter pub get
   ```

3. Configure Firebase:
   - Create a new Firebase project
   - Add your Firebase configuration files
   - Update the Firebase initialization in `lib/main.dart`

4. Configure Supabase:
   - Create a Supabase project
   - Update the Supabase credentials in `lib/main.dart`

5. Configure RevenueCat:
   - Create a RevenueCat account
   - Update the API keys in `lib/services/revenue_cat_service.dart`

6. Run the app:
   ```bash
   flutter run
   ```

## Architecture

The app follows a clean architecture pattern with:

- **BLo<PERSON> Pattern**: For state management
- **Service Locator**: For dependency injection
- **Repository Pattern**: For data access
- **Go Router**: For navigation

## Project Structure

```
lib/
├── blocs/          # BLoC state management
├── data/           # Data layer (repositories, services)
├── di/             # Dependency injection
├── l10n/           # Localization files
├── pages/          # UI screens
├── router/         # Navigation configuration
├── services/       # External services
├── theme/          # App theming
├── utils/          # Utility functions
└── widgets/        # Reusable widgets
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Getting Started

This project is a starting point for a Flutter application.

A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://docs.flutter.dev/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://docs.flutter.dev/cookbook)

For help getting started with Flutter development, view the
[online documentation](https://docs.flutter.dev/), which offers tutorials,
samples, guidance on mobile development, and a full API reference.
