import 'package:equatable/equatable.dart';

class Recipe extends Equatable {
  final String id;
  final String title;
  final String description;
  final List<String> ingredients;
  final List<String> instructions;
  final int cookingTimeMinutes;
  final int servings;
  final String difficulty; // 'Easy', 'Medium', 'Hard'
  final String category;
  final String? imageUrl;
  final List<String> tags;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isFavorite;
  final String? userId; // For user-specific recipes

  const Recipe({
    required this.id,
    required this.title,
    required this.description,
    required this.ingredients,
    required this.instructions,
    required this.cookingTimeMinutes,
    required this.servings,
    required this.difficulty,
    required this.category,
    this.imageUrl,
    required this.tags,
    required this.createdAt,
    required this.updatedAt,
    this.isFavorite = false,
    this.userId,
  });

  Recipe copyWith({
    String? id,
    String? title,
    String? description,
    List<String>? ingredients,
    List<String>? instructions,
    int? cookingTimeMinutes,
    int? servings,
    String? difficulty,
    String? category,
    String? imageUrl,
    List<String>? tags,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isFavorite,
    String? userId,
  }) {
    return Recipe(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      ingredients: ingredients ?? this.ingredients,
      instructions: instructions ?? this.instructions,
      cookingTimeMinutes: cookingTimeMinutes ?? this.cookingTimeMinutes,
      servings: servings ?? this.servings,
      difficulty: difficulty ?? this.difficulty,
      category: category ?? this.category,
      imageUrl: imageUrl ?? this.imageUrl,
      tags: tags ?? this.tags,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isFavorite: isFavorite ?? this.isFavorite,
      userId: userId ?? this.userId,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'ingredients': ingredients,
      'instructions': instructions,
      'cooking_time_minutes': cookingTimeMinutes,
      'servings': servings,
      'difficulty': difficulty,
      'category': category,
      'image_url': imageUrl,
      'tags': tags,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'is_favorite': isFavorite,
      'user_id': userId,
    };
  }

  factory Recipe.fromJson(Map<String, dynamic> json) {
    return Recipe(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      ingredients: List<String>.from(json['ingredients'] as List),
      instructions: List<String>.from(json['instructions'] as List),
      cookingTimeMinutes: json['cooking_time_minutes'] as int,
      servings: json['servings'] as int,
      difficulty: json['difficulty'] as String,
      category: json['category'] as String,
      imageUrl: json['image_url'] as String?,
      tags: List<String>.from(json['tags'] as List),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      isFavorite: json['is_favorite'] as bool? ?? false,
      userId: json['user_id'] as String?,
    );
  }

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        ingredients,
        instructions,
        cookingTimeMinutes,
        servings,
        difficulty,
        category,
        imageUrl,
        tags,
        createdAt,
        updatedAt,
        isFavorite,
        userId,
      ];
}
