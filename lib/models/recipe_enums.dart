// Centralized enums for recipe category and difficulty

enum RecipeCategory {
  other,     // 0
  breakfast, // 1
  lunch,     // 2
  dinner,    // 3
  snack,     // 4
  smoothie,  // 5
  salad,     // 6
  dessert,   // 7
  appetizer, // 8
}

String categoryLabel(RecipeCategory cat) {
  switch (cat) {
    case RecipeCategory.breakfast: return "Breakfast";
    case RecipeCategory.lunch: return "Lunch";
    case RecipeCategory.dinner: return "Dinner";
    case RecipeCategory.snack: return "Snack";
    case RecipeCategory.smoothie: return "Smoothie";
    case RecipeCategory.salad: return "Salad";
    case RecipeCategory.dessert: return "Dessert";
    case RecipeCategory.appetizer: return "Appetizer";
    default: return "Other";
  }
}

RecipeCategory categoryFromInt(int value) {
  if (value < 0 || value >= RecipeCategory.values.length) return RecipeCategory.other;
  return RecipeCategory.values[value];
}
int categoryToInt(RecipeCategory category) => category.index;


enum RecipeDifficulty {
  easy,   // 0
  medium, // 1
  hard,   // 2
}

String difficultyLabel(RecipeDifficulty diff) {
  switch (diff) {
    case RecipeDifficulty.easy: return "Easy";
    case RecipeDifficulty.medium: return "Medium";
    case RecipeDifficulty.hard: return "Hard";
    default: return "Easy";
  }
}

RecipeDifficulty difficultyFromInt(int value) {
  if (value < 0 || value >= RecipeDifficulty.values.length) return RecipeDifficulty.easy;
  return RecipeDifficulty.values[value];
}
int difficultyToInt(RecipeDifficulty difficulty) => difficulty.index; 