import 'package:equatable/equatable.dart';

class GroceryItem extends Equatable {
  final String id;
  final String name;
  final String quantity;
  final String category;
  final bool isCompleted;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? userId;
  final String? recipeId; // If item is from a recipe

  const GroceryItem({
    required this.id,
    required this.name,
    required this.quantity,
    required this.category,
    this.isCompleted = false,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
    this.userId,
    this.recipeId,
  });

  GroceryItem copyWith({
    String? id,
    String? name,
    String? quantity,
    String? category,
    bool? isCompleted,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? userId,
    String? recipeId,
  }) {
    return GroceryItem(
      id: id ?? this.id,
      name: name ?? this.name,
      quantity: quantity ?? this.quantity,
      category: category ?? this.category,
      isCompleted: isCompleted ?? this.isCompleted,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      userId: userId ?? this.userId,
      recipeId: recipeId ?? this.recipeId,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'quantity': quantity,
      'category': category,
      'is_completed': isCompleted,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'user_id': userId,
      'recipe_id': recipeId,
    };
  }

  factory GroceryItem.fromJson(Map<String, dynamic> json) {
    return GroceryItem(
      id: json['id'] as String,
      name: json['name'] as String,
      quantity: json['quantity'] as String,
      category: json['category'] as String,
      isCompleted: json['is_completed'] as bool? ?? false,
      notes: json['notes'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      userId: json['user_id'] as String?,
      recipeId: json['recipe_id'] as String?,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        quantity,
        category,
        isCompleted,
        notes,
        createdAt,
        updatedAt,
        userId,
        recipeId,
      ];
}
