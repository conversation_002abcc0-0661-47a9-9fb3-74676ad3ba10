class ApiKeys {
  // Supabase credentials
  // TODO: Replace with your actual Supabase credentials
  static const String supabaseUrl = 'YOUR_SUPABASE_URL';
  static const String supabaseAnonKey = 'YOUR_SUPABASE_ANON_KEY';

  // Google OAuth credentials
  // TODO: Replace with your actual Google OAuth credentials
  static const String googleIosClientId = 'YOUR_GOOGLE_IOS_CLIENT_ID';
  static const String googleServerClientId = 'YOUR_GOOGLE_SERVER_CLIENT_ID';

  // OAuth configuration
  static const String oauthCallbackPath = '/auth/v1/callback';
  static const String mobileDeepLink = 'com.pankaj6apr.ailacarte://login-callback';

  // Get the full OAuth callback URL based on platform
  static String getOAuthCallbackUrl(bool isWeb) =>
      isWeb ? '$supabaseUrl$oauthCallbackPath' : mobileDeepLink;

  // RevenueCat API keys
  // TODO: Replace with your actual RevenueCat API keys
  static const String revenueCatIosKey = 'YOUR_REVENUECAT_IOS_KEY';
  static const String revenueCatAndroidKey = 'YOUR_REVENUECAT_ANDROID_KEY';

  // Gemini AI API key (if you plan to add AI features)
  // TODO: Replace with your actual Gemini API key
  static const String geminiApiKey = 'YOUR_GEMINI_API_KEY';
}
