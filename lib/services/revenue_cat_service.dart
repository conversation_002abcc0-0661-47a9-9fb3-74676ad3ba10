import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:flutter/foundation.dart';

class RevenueCatService {
  static final RevenueCatService _instance = RevenueCatService._internal();
  factory RevenueCatService() => _instance;
  RevenueCatService._internal();

  bool _isInitialized = false;

  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // TODO: Replace with your actual RevenueCat API keys
      const apiKey = kDebugMode 
          ? 'your_debug_api_key_here' 
          : 'your_release_api_key_here';

      await Purchases.setLogLevel(LogLevel.debug);
      
      PurchasesConfiguration configuration = PurchasesConfiguration(apiKey);
      await Purchases.configure(configuration);
      
      _isInitialized = true;
    } catch (e) {
      debugPrint('RevenueCat initialization failed: $e');
    }
  }

  Future<void> identifyUser(String userId) async {
    try {
      await Purchases.logIn(userId);
    } catch (e) {
      debugPrint('Failed to identify user: $e');
    }
  }

  Future<CustomerInfo> getCustomerInfo() async {
    return await Purchases.getCustomerInfo();
  }

  Future<Offerings> getOfferings() async {
    return await Purchases.getOfferings();
  }

  Future<CustomerInfo> purchasePackage(Package package) async {
    return await Purchases.purchasePackage(package);
  }

  Future<CustomerInfo> restorePurchases() async {
    return await Purchases.restorePurchases();
  }

  Future<bool> isPremiumUser() async {
    try {
      final customerInfo = await getCustomerInfo();
      return customerInfo.entitlements.active.isNotEmpty;
    } catch (e) {
      debugPrint('Error checking premium status: $e');
      return false;
    }
  }

  Future<void> logout() async {
    try {
      await Purchases.logOut();
    } catch (e) {
      debugPrint('Failed to logout from RevenueCat: $e');
    }
  }
}
