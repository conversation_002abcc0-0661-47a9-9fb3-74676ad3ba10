import 'dart:async';
import 'dart:io';

import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:purchases_ui_flutter/purchases_ui_flutter.dart';
import 'package:flutter/foundation.dart';
import 'package:ailacarte/utils/api_keys.dart';

// Our custom SubscriptionStatus enum to represent subscription state
enum SubscriptionStatus {
  active,
  inactive,
  pending
}

class RevenueCatService {
  static final RevenueCatService _instance = RevenueCatService._internal();
  final _purchaseController = StreamController<SubscriptionStatus>.broadcast();

  // Use a static flag to track if we've configured RevenueCat
  static bool _configured = false;

  factory RevenueCatService() {
    return _instance;
  }

  RevenueCatService._internal();

  Stream<SubscriptionStatus> get purchaseStatus => _purchaseController.stream;

  /// Initialize the RevenueCat service
  ///
  /// This should be called when the app starts, after Firebase and Supabase are initialized.
  /// It configures the RevenueCat SDK with the appropriate API key for the current platform.
  Future<void> initialize() async {
    // If already configured, return immediately
    if (_configured) {
      return;
    }

    try {
      // Configure debug logs in debug mode
      if (kDebugMode) {
        await Purchases.setLogLevel(LogLevel.verbose);
      } else {
        await Purchases.setLogLevel(LogLevel.error);
      }

      // Get the API key based on the platform
      late String apiKey;

      if (Platform.isAndroid) {
        apiKey = ApiKeys.revenueCatAndroidKey;
      } else if (Platform.isIOS) {
        apiKey = ApiKeys.revenueCatIosKey;
      } else if (kIsWeb) {
        // For web, we'll use the Android key as a fallback
        apiKey = ApiKeys.revenueCatAndroidKey;
      } else {
        throw UnsupportedError('Unsupported platform');
      }

      // Log which key we're using (but don't log the actual key)
      debugPrint('Initializing RevenueCat for ${Platform.operatingSystem}');

      // Configure RevenueCat with API key
      final configuration = PurchasesConfiguration(apiKey);

      // Note: usesStoreKit2IfAvailable is no longer needed as it's enabled by default
      // in newer versions of the RevenueCat SDK
      configuration.shouldShowInAppMessagesAutomatically = true;
      await Purchases.configure(configuration);

      // Mark as configured
      _configured = true;

      // Set up customer info listener
      Purchases.addCustomerInfoUpdateListener((customerInfo) {
        _handleCustomerInfoUpdate(customerInfo);
      });

      // Check initial subscription status
      final customerInfo = await Purchases.getCustomerInfo();
      _handleCustomerInfoUpdate(customerInfo);
    } catch (e) {
      // Error initializing RevenueCat
      _purchaseController.add(SubscriptionStatus.inactive);
    }
  }

  void _handleCustomerInfoUpdate(CustomerInfo customerInfo) {
    final entitlements = customerInfo.entitlements.active;

    // Check if user has active entitlements
    if (entitlements.isNotEmpty) {
      _purchaseController.add(SubscriptionStatus.active);
    } else {
      _purchaseController.add(SubscriptionStatus.inactive);
    }
  }

  Future<void> identifyUser(String userId) async {
    try {
      await Purchases.logIn(userId);
    } catch (e) {
      debugPrint('Failed to identify user: $e');
    }
  }

  Future<CustomerInfo> getCustomerInfo() async {
    return await Purchases.getCustomerInfo();
  }

  Future<Offerings> getOfferings() async {
    return await Purchases.getOfferings();
  }

  Future<CustomerInfo> purchasePackage(Package package) async {
    return await Purchases.purchasePackage(package);
  }

  Future<CustomerInfo> restorePurchases() async {
    return await Purchases.restorePurchases();
  }

  Future<bool> isPremiumUser() async {
    try {
      final customerInfo = await getCustomerInfo();
      return customerInfo.entitlements.active.isNotEmpty;
    } catch (e) {
      debugPrint('Error checking premium status: $e');
      return false;
    }
  }

  Future<void> logout() async {
    try {
      await Purchases.logOut();
    } catch (e) {
      debugPrint('Failed to logout from RevenueCat: $e');
    }
  }
}
