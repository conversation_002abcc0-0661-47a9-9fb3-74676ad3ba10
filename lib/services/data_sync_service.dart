import 'dart:convert';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:ailacarte/database/database.dart';
import 'package:ailacarte/di/service_locator.dart';
import 'package:flutter/foundation.dart';
import 'package:uuid/uuid.dart';

class DataSyncService {
  static final DataSyncService _instance = DataSyncService._internal();
  factory DataSyncService() => _instance;
  DataSyncService._internal();

  final AppDatabase _database = getIt<AppDatabase>();
  final SupabaseClient _supabase = Supabase.instance.client;
  final Uuid _uuid = const Uuid();

  /// Sync all data from Supabase to local database
  Future<void> syncFromSupabase() async {
    try {
      debugPrint('Starting data sync from Supabase...');
      
      // Sync recipes
      await _syncRecipes();
      
      // Sync grocery items (if user is authenticated)
      if (_supabase.auth.currentUser != null) {
        await _syncGroceryItems();
      }
      
      debugPrint('Data sync completed successfully');
    } catch (e) {
      debugPrint('Error syncing data from Supabase: $e');
      rethrow;
    }
  }

  /// Sync recipes from Supabase
  Future<void> _syncRecipes() async {
    try {
      // Fetch recipes from Supabase
      final response = await _supabase
          .from('recipes')
          .select('*, ingredients(*)')
          .order('created_at', ascending: false);

      for (final recipeData in response) {
        // Insert/update recipe
        final recipe = RecipesCompanion.insert(
          id: recipeData['id'] as String,
          title: recipeData['title'] as String,
          description: recipeData['description'] as String? ?? '',
          instructions: recipeData['instructions'] as String? ?? '',
          cookingTimeMinutes: recipeData['cooking_time_minutes'] as int? ?? 0,
          servings: recipeData['servings'] as int? ?? 1,
          difficulty: recipeData['difficulty'] as String? ?? 'Easy',
          category: recipeData['category'] as String? ?? 'Other',
          imageUrls: recipeData['image_urls'] != null 
              ? jsonEncode(recipeData['image_urls']) 
              : null,
          tags: recipeData['tags'] != null 
              ? jsonEncode(recipeData['tags']) 
              : jsonEncode(<String>[]),
          userId: recipeData['user_id'] as String?,
          createdAt: DateTime.parse(recipeData['created_at'] as String),
          updatedAt: DateTime.parse(recipeData['updated_at'] as String),
          calories: recipeData['calories'] as int?,
          protein: recipeData['protein'] as double?,
          carbs: recipeData['carbs'] as double?,
          fat: recipeData['fat'] as double?,
          fiber: recipeData['fiber'] as double?,
          sugar: recipeData['sugar'] as double?,
          sodium: recipeData['sodium'] as double?,
        );

        await _database.into(_database.recipes).insertOnConflictUpdate(recipe);

        // Sync ingredients for this recipe
        await _database.deleteRecipeIngredients(recipeData['id'] as String);
        
        final ingredients = recipeData['ingredients'] as List<dynamic>? ?? [];
        for (final ingredientData in ingredients) {
          final ingredient = IngredientsCompanion.insert(
            id: ingredientData['id'] as String,
            recipeId: recipeData['id'] as String,
            name: ingredientData['name'] as String,
            amount: ingredientData['amount'] as String,
            unit: ingredientData['unit'] as String,
            order: ingredientData['order'] as int? ?? 0,
            createdAt: DateTime.parse(ingredientData['created_at'] as String),
          );
          
          await _database.into(_database.ingredients).insertOnConflictUpdate(ingredient);
        }
      }
    } catch (e) {
      debugPrint('Error syncing recipes: $e');
      rethrow;
    }
  }

  /// Sync grocery items from Supabase (user-specific)
  Future<void> _syncGroceryItems() async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) return;

      final response = await _supabase
          .from('grocery_items')
          .select('*')
          .eq('user_id', userId)
          .order('created_at', ascending: false);

      for (final itemData in response) {
        final item = GroceryItemsCompanion.insert(
          id: itemData['id'] as String,
          name: itemData['name'] as String,
          quantity: itemData['quantity'] as String,
          category: itemData['category'] as String,
          isCompleted: itemData['is_completed'] as bool? ?? false,
          notes: itemData['notes'] as String?,
          recipeId: itemData['recipe_id'] as String?,
          userId: itemData['user_id'] as String?,
          createdAt: DateTime.parse(itemData['created_at'] as String),
          updatedAt: DateTime.parse(itemData['updated_at'] as String),
        );

        await _database.into(_database.groceryItems).insertOnConflictUpdate(item);
      }
    } catch (e) {
      debugPrint('Error syncing grocery items: $e');
      rethrow;
    }
  }

  /// Upload local grocery item to Supabase
  Future<void> uploadGroceryItem(GroceryItem item) async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) return;

      await _supabase.from('grocery_items').upsert({
        'id': item.id,
        'name': item.name,
        'quantity': item.quantity,
        'category': item.category,
        'is_completed': item.isCompleted,
        'notes': item.notes,
        'recipe_id': item.recipeId,
        'user_id': userId,
        'created_at': item.createdAt.toIso8601String(),
        'updated_at': item.updatedAt.toIso8601String(),
      });
    } catch (e) {
      debugPrint('Error uploading grocery item: $e');
      rethrow;
    }
  }

  /// Delete grocery item from Supabase
  Future<void> deleteGroceryItemFromSupabase(String id) async {
    try {
      await _supabase.from('grocery_items').delete().eq('id', id);
    } catch (e) {
      debugPrint('Error deleting grocery item from Supabase: $e');
      rethrow;
    }
  }

  /// Generate ingredients as grocery items from a recipe
  Future<List<GroceryItem>> generateGroceryItemsFromRecipe(String recipeId) async {
    try {
      final ingredients = await _database.getRecipeIngredients(recipeId);
      final groceryItems = <GroceryItem>[];
      final now = DateTime.now();

      for (final ingredient in ingredients) {
        final groceryItem = GroceryItem(
          id: _uuid.v4(),
          name: ingredient.name,
          quantity: '${ingredient.amount} ${ingredient.unit}',
          category: _categorizeIngredient(ingredient.name),
          isCompleted: false,
          notes: 'From recipe',
          recipeId: recipeId,
          userId: _supabase.auth.currentUser?.id,
          createdAt: now,
          updatedAt: now,
        );
        
        groceryItems.add(groceryItem);
        
        // Insert into local database
        await _database.insertGroceryItem(GroceryItemsCompanion.insert(
          id: groceryItem.id,
          name: groceryItem.name,
          quantity: groceryItem.quantity,
          category: groceryItem.category,
          isCompleted: groceryItem.isCompleted,
          notes: groceryItem.notes,
          recipeId: groceryItem.recipeId,
          userId: groceryItem.userId,
          createdAt: groceryItem.createdAt,
          updatedAt: groceryItem.updatedAt,
        ));
        
        // Upload to Supabase if user is authenticated
        if (_supabase.auth.currentUser != null) {
          await uploadGroceryItem(groceryItem);
        }
      }

      return groceryItems;
    } catch (e) {
      debugPrint('Error generating grocery items from recipe: $e');
      rethrow;
    }
  }

  /// Simple ingredient categorization
  String _categorizeIngredient(String ingredientName) {
    final name = ingredientName.toLowerCase();
    
    if (name.contains('chicken') || name.contains('beef') || name.contains('pork') || 
        name.contains('fish') || name.contains('meat')) {
      return 'Meat';
    } else if (name.contains('milk') || name.contains('cheese') || name.contains('yogurt') || 
               name.contains('butter') || name.contains('cream')) {
      return 'Dairy';
    } else if (name.contains('tomato') || name.contains('onion') || name.contains('carrot') || 
               name.contains('pepper') || name.contains('lettuce') || name.contains('spinach')) {
      return 'Vegetables';
    } else if (name.contains('apple') || name.contains('banana') || name.contains('orange') || 
               name.contains('berry') || name.contains('grape')) {
      return 'Fruits';
    } else if (name.contains('bread') || name.contains('roll') || name.contains('bun')) {
      return 'Bakery';
    } else if (name.contains('pasta') || name.contains('rice') || name.contains('flour') || 
               name.contains('sugar') || name.contains('salt') || name.contains('oil')) {
      return 'Pantry';
    } else {
      return 'Other';
    }
  }
}
