import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';

class AnalyticsService {
  static final AnalyticsService _instance = AnalyticsService._internal();
  factory AnalyticsService() => _instance;
  AnalyticsService._internal();

  static AnalyticsService get instance => _instance;

  final FirebaseAnalytics _analytics = FirebaseAnalytics.instance;

  // Log app open
  Future<void> logAppOpen() async {
    try {
      await _analytics.logAppOpen();
    } catch (e) {
      debugPrint('Analytics error: $e');
    }
  }

  // Log app background
  Future<void> logAppBackground() async {
    try {
      await _analytics.logEvent(name: 'app_background');
    } catch (e) {
      debugPrint('Analytics error: $e');
    }
  }

  // Log screen view
  Future<void> logScreenView(String screenName) async {
    try {
      await _analytics.logScreenView(screenName: screenName);
    } catch (e) {
      debugPrint('Analytics error: $e');
    }
  }

  // Log login
  Future<void> logLogin(String method) async {
    try {
      await _analytics.logLogin(loginMethod: method);
    } catch (e) {
      debugPrint('Analytics error: $e');
    }
  }

  // Log sign up
  Future<void> logSignUp(String method) async {
    try {
      await _analytics.logSignUp(signUpMethod: method);
    } catch (e) {
      debugPrint('Analytics error: $e');
    }
  }

  // Log custom event
  Future<void> logEvent(String name, Map<String, Object>? parameters) async {
    try {
      await _analytics.logEvent(name: name, parameters: parameters);
    } catch (e) {
      debugPrint('Analytics error: $e');
    }
  }

  // Set user properties
  Future<void> setUserProperties({
    String? userId,
    String? email,
  }) async {
    try {
      if (userId != null) {
        await _analytics.setUserId(id: userId);
      }
      if (email != null) {
        await _analytics.setUserProperty(name: 'email', value: email);
      }
    } catch (e) {
      debugPrint('Analytics error: $e');
    }
  }

  // Log onboarding events
  Future<void> logOnboardingStep(String step) async {
    await logEvent('onboarding_step', {'step': step});
  }

  Future<void> logOnboardingCompleted() async {
    await logEvent('onboarding_completed', null);
  }

  // Log recipe events
  Future<void> logRecipeViewed(String recipeId) async {
    await logEvent('recipe_viewed', {'recipe_id': recipeId});
  }

  Future<void> logRecipeAdded(String recipeId) async {
    await logEvent('recipe_added', {'recipe_id': recipeId});
  }

  Future<void> logRecipeRemoved(String recipeId) async {
    await logEvent('recipe_removed', {'recipe_id': recipeId});
  }
}
