import 'dart:io';

import 'package:drift/drift.dart';
import 'package:drift/native.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as p;

part 'database.g.dart';

// Recipe table
class Recipes extends Table {
  TextColumn get id => text()();
  TextColumn get title => text()();
  TextColumn get description => text()();
  TextColumn get instructions => text()(); // Markdown content
  IntColumn get cookingTimeMinutes => integer()();
  IntColumn get servings => integer()();
  TextColumn get difficulty => text()();
  TextColumn get category => text()();
  TextColumn get imageUrls => text().nullable()(); // JSON array of image URLs
  TextColumn get tags => text()(); // JSON array of tags
  BoolColumn get isFavorite => boolean().withDefault(const Constant(false))();
  TextColumn get userId => text().nullable()();
  DateTimeColumn get createdAt => dateTime()();
  DateTimeColumn get updatedAt => dateTime()();
  
  // Nutrition information
  IntColumn get calories => integer().nullable()();
  RealColumn get protein => real().nullable()();
  RealColumn get carbs => real().nullable()();
  RealColumn get fat => real().nullable()();
  RealColumn get fiber => real().nullable()();
  RealColumn get sugar => real().nullable()();
  RealColumn get sodium => real().nullable()();

  @override
  Set<Column> get primaryKey => {id};
}

// Ingredients table (separate for better normalization)
class Ingredients extends Table {
  TextColumn get id => text()();
  TextColumn get recipeId => text()();
  TextColumn get name => text()();
  TextColumn get amount => text()();
  TextColumn get unit => text()();
  IntColumn get order => integer()(); // For ordering ingredients
  DateTimeColumn get createdAt => dateTime()();

  @override
  Set<Column> get primaryKey => {id};
}

// Grocery items table
class GroceryItems extends Table {
  TextColumn get id => text()();
  TextColumn get name => text()();
  TextColumn get quantity => text()();
  TextColumn get category => text()();
  BoolColumn get isCompleted => boolean().withDefault(const Constant(false))();
  TextColumn get notes => text().nullable()();
  TextColumn get recipeId => text().nullable()(); // If item is from a recipe
  TextColumn get userId => text().nullable()();
  DateTimeColumn get createdAt => dateTime()();
  DateTimeColumn get updatedAt => dateTime()();

  @override
  Set<Column> get primaryKey => {id};
}

@DriftDatabase(tables: [Recipes, Ingredients, GroceryItems])
class AppDatabase extends _$AppDatabase {
  AppDatabase() : super(_openConnection());

  @override
  int get schemaVersion => 1;

  // Recipe operations
  Future<List<Recipe>> getAllRecipes() => select(recipes).get();
  
  Future<Recipe?> getRecipeById(String id) => 
      (select(recipes)..where((r) => r.id.equals(id))).getSingleOrNull();
  
  Future<List<Recipe>> getFavoriteRecipes() => 
      (select(recipes)..where((r) => r.isFavorite.equals(true))).get();
  
  Future<List<Recipe>> getRecipesByCategory(String category) => 
      (select(recipes)..where((r) => r.category.equals(category))).get();
  
  Future<List<Recipe>> searchRecipes(String query) => 
      (select(recipes)..where((r) => r.title.contains(query) | r.description.contains(query))).get();

  Future<int> insertRecipe(RecipesCompanion recipe) => 
      into(recipes).insert(recipe);
  
  Future<bool> updateRecipe(RecipesCompanion recipe) => 
      update(recipes).replace(recipe);
  
  Future<int> deleteRecipe(String id) => 
      (delete(recipes)..where((r) => r.id.equals(id))).go();

  Future<bool> toggleRecipeFavorite(String id) async {
    final recipe = await getRecipeById(id);
    if (recipe != null) {
      await (update(recipes)..where((r) => r.id.equals(id)))
          .write(RecipesCompanion(isFavorite: Value(!recipe.isFavorite)));
      return !recipe.isFavorite;
    }
    return false;
  }

  // Ingredient operations
  Future<List<Ingredient>> getRecipeIngredients(String recipeId) => 
      (select(ingredients)..where((i) => i.recipeId.equals(recipeId))..orderBy([(i) => OrderingTerm.asc(i.order)])).get();
  
  Future<int> insertIngredient(IngredientsCompanion ingredient) => 
      into(ingredients).insert(ingredient);
  
  Future<int> deleteRecipeIngredients(String recipeId) => 
      (delete(ingredients)..where((i) => i.recipeId.equals(recipeId))).go();

  // Grocery item operations
  Future<List<GroceryItem>> getAllGroceryItems() => select(groceryItems).get();
  
  Future<List<GroceryItem>> getCompletedGroceryItems() => 
      (select(groceryItems)..where((g) => g.isCompleted.equals(true))).get();
  
  Future<List<GroceryItem>> getPendingGroceryItems() => 
      (select(groceryItems)..where((g) => g.isCompleted.equals(false))).get();

  Future<int> insertGroceryItem(GroceryItemsCompanion item) => 
      into(groceryItems).insert(item);
  
  Future<bool> updateGroceryItem(GroceryItemsCompanion item) => 
      update(groceryItems).replace(item);
  
  Future<int> deleteGroceryItem(String id) => 
      (delete(groceryItems)..where((g) => g.id.equals(id))).go();

  Future<bool> toggleGroceryItemCompletion(String id) async {
    final item = await (select(groceryItems)..where((g) => g.id.equals(id))).getSingleOrNull();
    if (item != null) {
      await (update(groceryItems)..where((g) => g.id.equals(id)))
          .write(GroceryItemsCompanion(isCompleted: Value(!item.isCompleted)));
      return !item.isCompleted;
    }
    return false;
  }

  Future<int> clearCompletedGroceryItems() => 
      (delete(groceryItems)..where((g) => g.isCompleted.equals(true))).go();
}

LazyDatabase _openConnection() {
  return LazyDatabase(() async {
    final dbFolder = await getApplicationDocumentsDirectory();
    final file = File(p.join(dbFolder.path, 'ailacarte.db'));
    return NativeDatabase.createInBackground(file);
  });
}
