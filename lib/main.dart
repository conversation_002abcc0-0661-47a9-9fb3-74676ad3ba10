import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_performance/firebase_performance.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:ailacarte/di/service_locator.dart';
import 'package:ailacarte/ailacarte_app.dart';
import 'package:ailacarte/services/notification_service.dart';
import 'package:ailacarte/services/revenue_cat_service.dart';
import 'package:flutter/foundation.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // Initialize Firebase
    await Firebase.initializeApp();

    // Enable debug mode for Firebase Analytics
    await FirebaseAnalytics.instance.setAnalyticsCollectionEnabled(true);
    if (kDebugMode) {
      // Enable debug logging for Firebase Analytics
      await FirebaseAnalytics.instance.setAnalyticsCollectionEnabled(true);
      await FirebaseAnalytics.instance.setSessionTimeoutDuration(const Duration(minutes: 30));

      // Log the app instance ID for debugging
      final appInstanceId = await FirebaseAnalytics.instance.appInstanceId;
      debugPrint('Firebase Analytics App Instance ID: $appInstanceId');
    }

    // Initialize Firebase Performance Monitoring if not in debug mode
    if (!kDebugMode) {
      await FirebasePerformance.instance.setPerformanceCollectionEnabled(true);
    }

    // Initialize Firebase Crashlytics
    FlutterError.onError = FirebaseCrashlytics.instance.recordFlutterFatalError;
    PlatformDispatcher.instance.onError = (error, stack) {
      FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
      return true;
    };

    // Initialize Supabase
    // TODO: Replace with your actual Supabase credentials
    await Supabase.initialize(
      url: 'YOUR_SUPABASE_URL',
      anonKey: 'YOUR_SUPABASE_ANON_KEY',
    );

    // Initialize dependency injection after Supabase
    await setupServiceLocator();

    // Initialize RevenueCat
    final revenueCatService = getIt<RevenueCatService>();
    await revenueCatService.initialize();
  } catch (e) {
    debugPrint('Failed to initialize app: $e');
    // Continue with app initialization even if some services fail
  }

  // Initialize notification service
  try {
    final notificationService = getIt<NotificationService>();
    await notificationService.initialize();
  } catch (e) {
    debugPrint('Failed to initialize notifications: $e');
  }

  // Initialize the app
  runApp(const AILaCarteApp());
}