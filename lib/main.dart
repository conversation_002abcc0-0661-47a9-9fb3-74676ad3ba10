

import 'dart:io';

import 'package:ailacarte/di/service_locator.dart';
import 'package:ailacarte/ailacarte_app.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_performance/firebase_performance.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import 'package:ailacarte/services/revenue_cat_service.dart';
import 'package:ailacarte/services/notification_service.dart';
import 'package:ailacarte/firebase_options.dart';
import 'package:ailacarte/utils/api_keys.dart';
import 'package:ailacarte/services/auth_service.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // Initialize Firebase
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    
    // Configure Firebase services
    await FirebaseAnalytics.instance.setAnalyticsCollectionEnabled(true);
    
    if (kDebugMode) {
      await FirebaseAnalytics.instance.setSessionTimeoutDuration(
        const Duration(minutes: 30),
      );
      final appInstanceId = await FirebaseAnalytics.instance.appInstanceId;
      debugPrint('Firebase Analytics App Instance ID: $appInstanceId');
    }
    
    if (!kDebugMode) {
      await FirebasePerformance.instance.setPerformanceCollectionEnabled(true);
    }
    
    FlutterError.onError = FirebaseCrashlytics.instance.recordFlutterFatalError;
    PlatformDispatcher.instance.onError = (error, stack) {
      FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
      return true;
    };
    
    // Initialize Supabase
    await Supabase.initialize(
      url: ApiKeys.supabaseUrl,
      anonKey: ApiKeys.supabaseAnonKey,
    );
    
    // Initialize dependency injection and services
    await setupServiceLocator();
    
    // Initialize RevenueCat service
    final revenueCatService = getIt<RevenueCatService>();
    await revenueCatService.initialize();
    
    // Get AuthService instance (no initialization needed)
    getIt<AuthService>();
    
  } catch (e) {
    debugPrint('Failed to initialize services: $e');
    // Continue with app initialization even if some services fail
  }

  // Initialize notification service
  try {
    final notificationService = getIt<NotificationService>();
    await notificationService.initialize();
  } catch (e) {
    debugPrint('Failed to initialize notifications: $e');
  }

  // Initialize the app
  runApp(const AILaCarteApp());
}