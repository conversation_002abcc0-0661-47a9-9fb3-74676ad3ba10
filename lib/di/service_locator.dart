import 'package:get_it/get_it.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:ailacarte_app/services/auth_service.dart';
import 'package:ailacarte_app/services/notification_service.dart';
import 'package:ailacarte_app/services/revenue_cat_service.dart';
import 'package:ailacarte_app/services/analytics_service.dart';
import 'package:ailacarte_app/data/settings_preference.dart';

final GetIt getIt = GetIt.instance;

Future<void> setupServiceLocator() async {
  // Register SharedPreferences
  final sharedPreferences = await SharedPreferences.getInstance();
  getIt.registerSingleton<SharedPreferences>(sharedPreferences);

  // Register Settings Preferences
  getIt.registerSingleton<SettingsPreferences>(
    SettingsPreferences(sharedPreferences),
  );

  // Register Services
  getIt.registerSingleton<AuthService>(AuthService());
  getIt.registerSingleton<NotificationService>(NotificationService());
  getIt.registerSingleton<RevenueCatService>(RevenueCatService());
  getIt.registerSingleton<AnalyticsService>(AnalyticsService());
}
