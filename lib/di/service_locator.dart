import 'package:get_it/get_it.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:ailacarte/services/auth_service.dart';
import 'package:ailacarte/services/notification_service.dart';
import 'package:ailacarte/services/revenue_cat_service.dart';
import 'package:ailacarte/services/analytics_service.dart';
import 'package:ailacarte/data/settings_preference.dart';
import 'package:ailacarte/database/database.dart';
import 'package:ailacarte/services/data_sync_service.dart';

final GetIt getIt = GetIt.instance;

Future<void> setupServiceLocator() async {
  // Register SharedPreferences asynchronously
  final sharedPreferences = await SharedPreferences.getInstance();
  getIt.registerSingleton<SharedPreferences>(sharedPreferences);

  // Register Settings Preferences
  getIt.registerLazySingleton<SettingsPreferences>(
    () => SettingsPreferences(getIt<SharedPreferences>()),
  );

  // Register database
  getIt.registerLazySingleton<AppDatabase>(() => AppDatabase());

  // Register Services as Lazy Singletons to handle circular dependencies
  getIt.registerLazySingleton<AuthService>(() => AuthService());
  getIt.registerLazySingleton<NotificationService>(() => NotificationService());
  getIt.registerLazySingleton<RevenueCatService>(() => RevenueCatService());
  getIt.registerLazySingleton<AnalyticsService>(() => AnalyticsService());
  getIt.registerLazySingleton<DataSyncService>(() => DataSyncService());

  // Initialize services that need it
  await getIt.allReady();
}
