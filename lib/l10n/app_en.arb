{"@@locale": "en", "appTitle": "AI La Carte", "@appTitle": {"description": "The title of the application"}, "welcome": "Welcome", "@welcome": {"description": "Welcome message"}, "welcomeToApp": "Welcome to AI La Carte", "@welcomeToApp": {"description": "Welcome to app message"}, "welcomeMessage": "Your AI-powered recipe collection companion", "@welcomeMessage": {"description": "Welcome message description"}, "selectLanguage": "Select Language", "@selectLanguage": {"description": "Select language prompt"}, "getStarted": "Get Started", "@getStarted": {"description": "Get started button text"}, "next": "Next", "@next": {"description": "Next button text"}, "skip": "<PERSON><PERSON>", "@skip": {"description": "Skip button text"}, "done": "Done", "@done": {"description": "Done button text"}, "themeSelection": "Choose Your Theme", "@themeSelection": {"description": "Theme selection title"}, "lightTheme": "Light", "@lightTheme": {"description": "Light theme option"}, "darkTheme": "Dark", "@darkTheme": {"description": "Dark theme option"}, "systemTheme": "System", "@systemTheme": {"description": "System theme option"}, "notifications": "Notifications", "@notifications": {"description": "Notifications title"}, "notificationPermission": "Enable notifications to get recipe reminders and updates", "@notificationPermission": {"description": "Notification permission description"}, "enableNotifications": "Enable Notifications", "@enableNotifications": {"description": "Enable notifications button"}, "recipes": "Recipes", "@recipes": {"description": "Recipes tab title"}, "groceryList": "Grocery List", "@groceryList": {"description": "Grocery list tab title"}, "settings": "Settings", "@settings": {"description": "Settings tab title"}, "account": "Account", "@account": {"description": "Account section title"}, "login": "<PERSON><PERSON>", "@login": {"description": "Login button text"}, "logout": "Logout", "@logout": {"description": "Logout button text"}, "email": "Email", "@email": {"description": "Email field label"}, "password": "Password", "@password": {"description": "Password field label"}, "splashCheckingAuth": "Checking authentication...", "@splashCheckingAuth": {"description": "Splash screen auth check message"}, "splashSyncingData": "Syncing data...", "@splashSyncingData": {"description": "Splash screen data sync message"}, "splashErrorTitle": "Initialization Error", "@splashErrorTitle": {"description": "Splash screen error title"}, "splashRetryButton": "Retry", "@splashRetryButton": {"description": "Splash screen retry button"}}