import 'package:flutter/material.dart';

class CustomTheme {
  // Primary colors
  static const Color primaryColor = Color(0xFF6B73FF);
  static const Color secondaryColor = Color(0xFF9C27B0);
  
  // Gradient colors
  static const Color gradientStart = Color(0xFF667eea);
  static const Color gradientEnd = Color(0xFF764ba2);
  
  // Recipe-themed colors
  static const Color recipeOrange = Color(0xFFFF6B35);
  static const Color recipeGreen = Color(0xFF4CAF50);
  static const Color recipeRed = Color(0xFFE53E3E);
  static const Color recipeYellow = Color(0xFFFFC107);

  // Get primary icon color based on theme
  static Color getPrimaryIconColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? Colors.white
        : primaryColor;
  }

  // Get secondary icon color based on theme
  static Color getSecondaryIconColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? Colors.white.withAlpha((0.6 * 255).round())
        : Colors.grey.shade600;
  }

  // Get navigation bar shadows
  static List<BoxShadow> getNavBarShadows(BuildContext context) {
    return [
      BoxShadow(
        color: Theme.of(context).brightness == Brightness.dark
            ? Colors.black.withAlpha((0.3 * 255).round())
            : Colors.black.withAlpha((0.1 * 255).round()),
        blurRadius: 10,
        offset: const Offset(0, 5),
      ),
    ];
  }

  // Get card shadows
  static List<BoxShadow> getCardShadows(BuildContext context) {
    return [
      BoxShadow(
        color: Theme.of(context).brightness == Brightness.dark
            ? Colors.black.withAlpha((0.2 * 255).round())
            : Colors.black.withAlpha((0.08 * 255).round()),
        blurRadius: 8,
        offset: const Offset(0, 2),
      ),
    ];
  }
}
