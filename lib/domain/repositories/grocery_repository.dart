import 'package:ailacarte/models/grocery_item.dart';

abstract class GroceryRepository {
  /// Get all grocery items for the current user
  Future<List<GroceryItem>> getGroceryItems();

  /// Get grocery items by category
  Future<List<GroceryItem>> getGroceryItemsByCategory(String category);

  /// Get completed grocery items
  Future<List<GroceryItem>> getCompletedItems();

  /// Get pending grocery items
  Future<List<GroceryItem>> getPendingItems();

  /// Create a new grocery item
  Future<GroceryItem> createGroceryItem(GroceryItem item);

  /// Update an existing grocery item
  Future<GroceryItem> updateGroceryItem(GroceryItem item);

  /// Delete a grocery item
  Future<void> deleteGroceryItem(String id);

  /// Toggle completion status
  Future<GroceryItem> toggleCompletion(String id);

  /// Clear all completed items
  Future<void> clearCompletedItems();

  /// Generate grocery list from recipe
  Future<List<GroceryItem>> generateFromRecipe(String recipeId);

  /// Get all categories
  Future<List<String>> getCategories();

  /// Sync grocery items with remote server
  Future<void> syncGroceryItems();
}
