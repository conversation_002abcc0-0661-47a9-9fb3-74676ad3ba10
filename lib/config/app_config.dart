import 'package:flutter/foundation.dart';

/// Centralized configuration for all API keys and service configurations
class AppConfig {
  // Firebase Configuration
  static const Map<String, dynamic> firebaseConfig = {
    'apiKey': 'YOUR_FIREBASE_API_KEY',
    'appId': 'YOUR_FIREBASE_APP_ID',
    'messagingSenderId': 'YOUR_MESSAGING_SENDER_ID',
    'projectId': 'YOUR_PROJECT_ID',
    'storageBucket': 'YOUR_STORAGE_BUCKET',
  };

  // Supabase Configuration
  static const String supabaseUrl = 'YOUR_SUPABASE_URL';
  static const String supabaseAnonKey = 'YOUR_SUPABASE_ANON_KEY';

  // RevenueCat Configuration
  static const String revenueCatIosKey = 'YOUR_REVENUECAT_IOS_KEY';
  static const String revenueCatAndroidKey = 'YOUR_REVENUECAT_ANDROID_KEY';

  // Environment Checks
  static bool get isFirebaseConfigured => !kDebugMode ||
      (firebaseConfig['apiKey'] != 'YOUR_FIREBASE_API_KEY' &&
          firebaseConfig['appId'] != 'YOUR_FIREBASE_APP_ID');

  static bool get isSupabaseConfigured =>
      supabaseUrl != 'YOUR_SUPABASE_URL' &&
          supabaseAnonKey != 'YOUR_SUPABASE_ANON_KEY';

  static bool get isRevenueCatConfigured =>
      revenueCatIosKey != 'YOUR_REVENUECAT_IOS_KEY' &&
          revenueCatAndroidKey != 'YOUR_REVENUECAT_ANDROID_KEY';

  // Add other configuration getters as needed
  static bool get isConfigured => 
      isFirebaseConfigured && 
      isSupabaseConfigured && 
      isRevenueCatConfigured;
}
