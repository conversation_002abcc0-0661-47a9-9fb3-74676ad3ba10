/// Base exception class for the application
abstract class AppException implements Exception {
  final String message;
  final String? code;

  const AppException(this.message, {this.code});

  @override
  String toString() => 'AppException: $message${code != null ? ' (Code: $code)' : ''}';
}

/// Exception thrown when there's a network-related error
class NetworkException extends AppException {
  const NetworkException(super.message, {super.code});
}

/// Exception thrown when there's an authentication error
class AuthException extends AppException {
  const AuthException(super.message, {super.code});
}

/// Exception thrown when there's a server error
class ServerException extends AppException {
  const ServerException(super.message, {super.code});
}

/// Exception thrown when there's a cache error
class CacheException extends AppException {
  const CacheException(super.message, {super.code});
}

/// Exception thrown when there's a validation error
class ValidationException extends AppException {
  const ValidationException(super.message, {super.code});
}

/// Exception thrown when there's a permission error
class PermissionException extends AppException {
  const PermissionException(super.message, {super.code});
}

/// Exception thrown when there's a purchase/subscription error
class PurchaseException extends AppException {
  const PurchaseException(super.message, {super.code});
}
