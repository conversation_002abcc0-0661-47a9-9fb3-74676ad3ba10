import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:ailacarte/data/settings_preference.dart';
import 'package:ailacarte/services/auth_service.dart';
import 'package:ailacarte/services/revenue_cat_service.dart';
import 'package:ailacarte/pages/onboarding/onboarding_screen.dart';
import 'package:ailacarte/pages/splash_screen.dart';

class GatekeeperScreen extends StatefulWidget {
  const GatekeeperScreen({super.key});

  @override
  State<GatekeeperScreen> createState() => _GatekeeperScreenState();
}

class _GatekeeperScreenState extends State<GatekeeperScreen> {
  // 0 = onboarding, 1 = login, 2 = paywall, 3 = splash, 4 = main
  int _step = 0;

  @override
  void initState() {
    super.initState();
    _checkState();
  }

  Future<void> _checkState() async {
    try {
      final settingsPreferences = await SettingsPreferences.getInstance();
      final isOnboardingCompleted = settingsPreferences.isOnboardingCompleted;
      if (!isOnboardingCompleted) {
        if (mounted) setState(() => _step = 0);
        return;
      }
      
      final authService = AuthService();
      final isLoggedIn = authService.currentUser != null;
      if (!isLoggedIn) {
        if (mounted) setState(() => _step = 1);
        return;
      }
      
      final revenueCatService = RevenueCatService();
      final isPremium = await revenueCatService.isSubscriptionActive();
      if (!isPremium) {
        final paywallShown = settingsPreferences.isPaywallShown;
        if (!paywallShown) {
          if (mounted) setState(() => _step = 2);
          return;
        } else {
          if (mounted) setState(() => _step = 3);
          return;
        }
      }
      if (mounted) setState(() => _step = 3);
    } catch (e) {
      debugPrint('Error in _checkState: $e');
      // Fallback to onboarding if there's an error
      if (mounted) setState(() => _step = 0);
    }
  }

  void _onOnboardingComplete() {
    _checkState();
  }





  void _onSplashComplete() {
    // All done, go to main app
    GoRouter.of(context).go('/');
  }

  @override
  Widget build(BuildContext context) {
    switch (_step) {
      case 0:
        return OnboardingScreen(onComplete: _onOnboardingComplete);
      case 1:
     //   return LoginRegisterPage(onLoginSuccess: _onLoginSuccess);
      case 2:
       // return PaywallScreen(onComplete: _onPaywallComplete);
      case 3:
        return SplashScreen(onComplete: _onSplashComplete);
      default:
        return const SizedBox.shrink();
    }
  }
}
