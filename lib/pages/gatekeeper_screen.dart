import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:ailacarte/di/service_locator.dart';
import 'package:ailacarte/data/settings_preference.dart';
import 'package:ailacarte/services/auth_service.dart';
import 'package:ailacarte/pages/splash_screen.dart';

class GatekeeperScreen extends StatefulWidget {
  const GatekeeperScreen({super.key});

  @override
  State<GatekeeperScreen> createState() => _GatekeeperScreenState();
}

class _GatekeeperScreenState extends State<GatekeeperScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _handleNavigation();
    });
  }

  void _handleNavigation() {
    // Show splash screen first, then handle navigation logic
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => SplashScreen(
          onComplete: _onSplashComplete,
        ),
      ),
    );
  }

  void _onSplashComplete() async {
    if (!mounted) return;

    final settingsPreferences = getIt<SettingsPreferences>();
    final authService = getIt<AuthService>();

    // Check if onboarding is completed
    final isOnboardingCompleted = settingsPreferences.isOnboardingCompleted;
    
    if (!isOnboardingCompleted) {
      // Go to onboarding
      if (mounted) {
        context.go('/onboarding');
      }
      return;
    }

    // Check if user is authenticated
    final currentUser = authService.currentUser;
    
    if (currentUser == null) {
      // Go to login
      if (mounted) {
        context.go('/login');
      }
      return;
    }

    // User is authenticated and onboarding is complete, go to main app
    if (mounted) {
      context.go('/');
    }
  }

  @override
  Widget build(BuildContext context) {
    // This screen should never be visible as it immediately navigates
    return const Scaffold(
      body: Center(
        child: CircularProgressIndicator(),
      ),
    );
  }
}
