import 'package:flutter/material.dart';
import 'package:ailacarte_app/theme/custom_theme.dart';
import 'package:ailacarte_app/di/service_locator.dart';
import 'package:ailacarte_app/services/analytics_service.dart';
import 'package:ailacarte_app/services/revenue_cat_service.dart';
import 'package:ailacarte_app/services/auth_service.dart';
import 'package:ailacarte_app/data/settings_preference.dart';
import 'package:flutter/foundation.dart';

class SplashScreen extends StatefulWidget {
  final VoidCallback onComplete;
  const SplashScreen({super.key, required this.onComplete});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  String _status = 'Initializing...';
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    // Use addPostFrameCallback to ensure the widget is fully built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeApp();
    });
  }

  Future<void> _initializeApp() async {
    if (!mounted) return;

    try {
      // Update status
      setState(() => _status = 'Checking authentication...');
      
      // Get current auth state
      final authService = getIt<AuthService>();
      final currentUser = authService.currentUser;
      
      if (currentUser != null) {
        // User is logged in - proceed with initialization
        setState(() => _status = 'Syncing data...');
        
        // Update analytics
        await AnalyticsService.instance.setUserProperties(
          userId: currentUser.id,
          email: currentUser.email,
        );
        
        // Identify user with RevenueCat if email is available
        if (currentUser.email != null) {
          await RevenueCatService().identifyUser(currentUser.email!);
        }
        
        // Call the onComplete callback
        if (mounted) widget.onComplete();
      } else {
        // User not logged in - call the onComplete callback
        if (mounted) widget.onComplete();
      }
    } catch (e) {
      debugPrint('Error during initialization: $e');
      if (!mounted) return;
      
      // Show error and allow retry
      setState(() {
        _status = 'Error: ${e.toString()}';
        _hasError = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [CustomTheme.gradientStart, CustomTheme.gradientEnd],
          ),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const SizedBox(height: 24),
              // App Icon
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(24),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 10,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.restaurant_menu,
                  size: 60,
                  color: CustomTheme.primaryColor,
                ),
              ),
              const SizedBox(height: 32),
              // App Title
              const Text(
                'AI La Carte',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  letterSpacing: 1.2,
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                'Your AI-powered recipe companion',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 16,
                  fontWeight: FontWeight.w400,
                ),
              ),
              const SizedBox(height: 48),
              if (_hasError) ...[
                const Icon(Icons.error_outline, color: Colors.white, size: 48),
                const SizedBox(height: 16),
                const Text(
                  'Initialization Error',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 32.0),
                  child: Text(
                    _status,
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      color: Colors.white70,
                      fontSize: 14,
                    ),
                  ),
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: () {
                    setState(() {
                      _hasError = false;
                      _status = 'Initializing...';
                    });
                    _initializeApp();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white,
                    foregroundColor: CustomTheme.primaryColor,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 32,
                      vertical: 12,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                    elevation: 2,
                  ),
                  child: const Text('Retry'),
                ),
              ] else ...[
                const CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  strokeWidth: 3.0,
                ),
                const SizedBox(height: 24),
                Text(
                  _status,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    letterSpacing: 0.5,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
