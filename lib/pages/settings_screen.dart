import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:ailacarte/theme/custom_theme.dart';
import 'package:ailacarte/widgets/themed_background.dart';
import 'package:ailacarte/widgets/setting_item.dart';
import 'package:ailacarte/blocs/theme_bloc/theme_bloc.dart';
import 'package:ailacarte/blocs/language_bloc/language_bloc.dart';
import 'package:ailacarte/services/auth_service.dart';
import 'package:ailacarte/services/analytics_service.dart';
import 'package:ailacarte/di/service_locator.dart';
import 'package:ailacarte/l10n/l10n.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:package_info_plus/package_info_plus.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final ScrollController _scrollController = ScrollController();
  bool _showTitle = false;
  String _appVersion = '';

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    _loadAppInfo();
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.hasClients) {
      final showTitle = _scrollController.offset > 30;
      if (showTitle != _showTitle) {
        setState(() {
          _showTitle = showTitle;
        });
      }
    }
  }

  Future<void> _loadAppInfo() async {
    final packageInfo = await PackageInfo.fromPlatform();
    setState(() {
      _appVersion = '${packageInfo.version} (${packageInfo.buildNumber})';
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final authService = getIt<AuthService>();
    final currentUser = authService.currentUser;

    return ThemedBackground(
      showGradient: false,
      child: CustomScrollView(
        controller: _scrollController,
        slivers: [
          // App Bar
          SliverAppBar(
            expandedHeight: 120,
            floating: false,
            pinned: true,
            backgroundColor: Colors.transparent,
            elevation: 0,
            flexibleSpace: FlexibleSpaceBar(
              title: _showTitle
                  ? Text(
                      'Settings',
                      style: TextStyle(
                        color: theme.colorScheme.onSurface,
                        fontWeight: FontWeight.bold,
                      ),
                    )
                  : null,
              background: Container(
                padding: const EdgeInsets.fromLTRB(24, 60, 24, 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Text(
                      'Settings',
                      style: theme.textTheme.headlineLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.primary,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Customize your app experience',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.textTheme.bodyMedium?.color?.withOpacity(0.7),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Settings Content
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.fromLTRB(0, 16, 0, 100),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Account Section
                  _buildSectionHeader('Account'),
                  if (currentUser != null) ...[
                    SettingItem(
                      icon: Icons.account_circle,
                      title: currentUser.email ?? 'User',
                      subtitle: 'Manage your account',
                      onTap: () {
                        // TODO: Navigate to profile
                      },
                    ),
                  ] else ...[
                    SettingItem(
                      icon: Icons.login,
                      title: 'Sign In',
                      subtitle: 'Access your account',
                      onTap: () => context.go('/login'),
                    ),
                  ],
                  
                  const SizedBox(height: 24),

                  // Appearance Section
                  _buildSectionHeader('Appearance'),
                  BlocBuilder<ThemeBloc, ThemeState>(
                    builder: (context, state) {
                      return SettingItem(
                        icon: Icons.palette,
                        title: 'Theme',
                        subtitle: _getThemeName(state.themeMode),
                        onTap: () => _showThemeDialog(context),
                      );
                    },
                  ),
                  BlocBuilder<LanguageBloc, LanguageState>(
                    builder: (context, state) {
                      return SettingItem(
                        icon: Icons.language,
                        title: AppLocalizations.of(context)!.selectLanguage,
                        subtitle: L10n.getLanguageName(state.locale.languageCode),
                        onTap: () => _showLanguageDialog(context),
                      );
                    },
                  ),

                  const SizedBox(height: 24),

                  // Notifications Section
                  _buildSectionHeader('Notifications'),
                  SettingItem(
                    icon: Icons.notifications,
                    title: 'Push Notifications',
                    subtitle: 'Recipe reminders and updates',
                    trailing: Switch(
                      value: true, // TODO: Get from settings
                      onChanged: (value) {
                        // TODO: Update notification settings
                      },
                    ),
                    onTap: null,
                  ),

                  const SizedBox(height: 24),

                  // Premium Section
                  _buildSectionHeader('Premium'),
                  SettingItem(
                    icon: Icons.star,
                    title: 'Upgrade to Premium',
                    subtitle: 'Unlock all features',
                    iconColor: CustomTheme.recipeYellow,
                    onTap: () => context.go('/paywall'),
                  ),

                  const SizedBox(height: 24),

                  // Support Section
                  _buildSectionHeader('Support'),
                  SettingItem(
                    icon: Icons.help,
                    title: 'Help & FAQ',
                    subtitle: 'Get help and find answers',
                    onTap: () {
                      // TODO: Navigate to help
                    },
                  ),
                  SettingItem(
                    icon: Icons.feedback,
                    title: 'Send Feedback',
                    subtitle: 'Help us improve the app',
                    onTap: () {
                      // TODO: Open feedback
                    },
                  ),
                  SettingItem(
                    icon: Icons.info,
                    title: 'About',
                    subtitle: 'Version $_appVersion',
                    onTap: () {
                      // TODO: Show about dialog
                    },
                  ),

                  const SizedBox(height: 24),

                  // Account Actions
                  if (currentUser != null) ...[
                    _buildSectionHeader('Account'),
                    SettingItem(
                      icon: Icons.logout,
                      title: 'Sign Out',
                      subtitle: 'Sign out of your account',
                      iconColor: Colors.red,
                      onTap: () => _showSignOutDialog(context),
                      showDivider: false,
                    ),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    final theme = Theme.of(context);
    
    return Padding(
      padding: const EdgeInsets.fromLTRB(24, 0, 24, 8),
      child: Text(
        title,
        style: theme.textTheme.titleSmall?.copyWith(
          fontWeight: FontWeight.bold,
          color: theme.colorScheme.primary,
          letterSpacing: 0.5,
        ),
      ),
    );
  }

  String _getThemeName(ThemeMode themeMode) {
    switch (themeMode) {
      case ThemeMode.light:
        return 'Light';
      case ThemeMode.dark:
        return 'Dark';
      case ThemeMode.system:
        return 'System';
    }
  }

  void _showThemeDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Choose Theme'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildThemeOption(context, 'Light', ThemeMode.light),
            _buildThemeOption(context, 'Dark', ThemeMode.dark),
            _buildThemeOption(context, 'System', ThemeMode.system),
          ],
        ),
      ),
    );
  }

  Widget _buildThemeOption(BuildContext context, String title, ThemeMode mode) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, state) {
        return ListTile(
          title: Text(title),
          leading: Radio<ThemeMode>(
            value: mode,
            groupValue: state.themeMode,
            onChanged: (value) {
              if (value != null) {
                context.read<ThemeBloc>().add(ChangeThemeEvent(value));
                Navigator.of(context).pop();
              }
            },
          ),
          onTap: () {
            context.read<ThemeBloc>().add(ChangeThemeEvent(mode));
            Navigator.of(context).pop();
          },
        );
      },
    );
  }

  void _showLanguageDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(AppLocalizations.of(context)!.selectLanguage),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: L10n.supportedLocales.map((locale) {
            return _buildLanguageOption(
              context,
              L10n.getLanguageName(locale.languageCode),
              locale,
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildLanguageOption(BuildContext context, String title, Locale locale) {
    return BlocBuilder<LanguageBloc, LanguageState>(
      builder: (context, state) {
        return ListTile(
          title: Text(title),
          leading: Radio<Locale>(
            value: locale,
            groupValue: state.locale,
            onChanged: (value) {
              if (value != null) {
                context.read<LanguageBloc>().add(ChangeLanguageEvent(value));
                Navigator.of(context).pop();
              }
            },
          ),
          onTap: () {
            context.read<LanguageBloc>().add(ChangeLanguageEvent(locale));
            Navigator.of(context).pop();
          },
        );
      },
    );
  }

  void _showSignOutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sign Out'),
        content: const Text('Are you sure you want to sign out?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              try {
                final authService = getIt<AuthService>();
                await authService.signOut();
                await AnalyticsService.instance.logEvent('user_signed_out', null);
                if (context.mounted) {
                  context.go('/login');
                }
              } catch (e) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Sign out failed: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            child: const Text('Sign Out'),
          ),
        ],
      ),
    );
  }
}
