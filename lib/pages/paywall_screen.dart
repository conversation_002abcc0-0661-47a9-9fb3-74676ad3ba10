import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:ailacarte/widgets/themed_background.dart';
import 'package:ailacarte/theme/custom_theme.dart';
import 'package:ailacarte/services/revenue_cat_service.dart';
import 'package:ailacarte/services/analytics_service.dart';
import 'package:ailacarte/di/service_locator.dart';

class PaywallScreen extends StatefulWidget {
  final VoidCallback onComplete;

  const PaywallScreen({
    super.key,
    required this.onComplete,
  });

  @override
  State<PaywallScreen> createState() => _PaywallScreenState();
}

class _PaywallScreenState extends State<PaywallScreen> {
  bool _isLoading = true;
  bool _isPurchasing = false;
  Offerings? _offerings;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadOfferings();
  }

  Future<void> _loadOfferings() async {
    try {
      final revenueCatService = getIt<RevenueCatService>();
      final offerings = await revenueCatService.getOfferings();
      
      if (mounted) {
        setState(() {
          _offerings = offerings;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _purchasePackage(Package package) async {
    setState(() {
      _isPurchasing = true;
    });

    try {
      final revenueCatService = getIt<RevenueCatService>();
      await revenueCatService.purchasePackage(package);
      
      // Log purchase event
      await AnalyticsService.instance.logEvent('premium_purchased', {
        'package_id': package.identifier,
        'price': package.storeProduct.priceString,
      });
      
      if (mounted) {
        context.go('/login');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Purchase failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isPurchasing = false;
        });
      }
    }
  }

  Future<void> _restorePurchases() async {
    try {
      final revenueCatService = getIt<RevenueCatService>();
      final customerInfo = await revenueCatService.restorePurchases();
      
      if (customerInfo.entitlements.active.isNotEmpty) {
        if (mounted) {
          context.go('/login');
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('No active purchases found'),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Restore failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_error != null) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              Text('Error loading offers: $_error'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => context.go('/login'),
                child: const Text('Continue without Premium'),
              ),
            ],
          ),
        ),
      );
    }

    final offering = _offerings?.current;
    if (offering == null || offering.availablePackages.isEmpty) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text('No offers available'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => context.go('/login'),
                child: const Text('Continue'),
              ),
            ],
          ),
        ),
      );
    }

    return ThemedBackground(
      child: Column(
        children: [
          // Header
          Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              children: [
                const SizedBox(height: 32),
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: CustomTheme.primaryColor,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: CustomTheme.primaryColor.withAlpha((0.3 * 255).round()),
                        blurRadius: 20,
                        offset: const Offset(0, 10),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.star,
                    size: 40,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 24),
                Text(
                  'Unlock Premium Features',
                  style: theme.textTheme.headlineLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.primary,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Text(
                  'Get unlimited access to all recipes and features',
                  style: theme.textTheme.bodyLarge,
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),

          // Features list
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 24.0),
              child: Column(
                children: [
                  _buildFeatureItem(
                    context,
                    icon: Icons.auto_awesome,
                    title: 'Unlimited AI Recipes',
                    subtitle: 'Generate personalized recipes without limits',
                  ),
                  _buildFeatureItem(
                    context,
                    icon: Icons.cloud_sync,
                    title: 'Cloud Sync',
                    subtitle: 'Access your recipes across all devices',
                  ),
                  _buildFeatureItem(
                    context,
                    icon: Icons.offline_bolt,
                    title: 'Offline Access',
                    subtitle: 'Cook without internet connection',
                  ),
                  _buildFeatureItem(
                    context,
                    icon: Icons.support_agent,
                    title: 'Priority Support',
                    subtitle: 'Get help when you need it most',
                  ),
                  const SizedBox(height: 24),
                ],
              ),
            ),
          ),

          // Pricing packages
          Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              children: [
                ...offering.availablePackages.map((package) =>
                  _buildPackageOption(package)
                ),
                const SizedBox(height: 16),
                
                // Restore purchases button
                TextButton(
                  onPressed: _restorePurchases,
                  child: Text(
                    'Restore Purchases',
                    style: TextStyle(
                      color: theme.colorScheme.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                
                // Continue without premium
                TextButton(
                  onPressed: () => context.go('/login'),
                  child: Text(
                    'Continue without Premium',
                    style: TextStyle(
                      color: theme.colorScheme.onSurface.withAlpha((0.6 * 255).round()),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withAlpha((0.1 * 255).round()),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              color: theme.colorScheme.primary,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.textTheme.bodyMedium?.color?.withAlpha((0.7 * 255).round()),
                  ),
                ),
              ],
            ),
          ),
          Icon(
            Icons.check_circle,
            color: theme.colorScheme.primary,
            size: 20,
          ),
        ],
      ),
    );
  }

  Widget _buildPackageOption(Package package) {
    final theme = Theme.of(context);
    final isPopular = package.packageType == PackageType.monthly;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Stack(
        children: [
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              gradient: isPopular
                  ? LinearGradient(
                      colors: [
                        CustomTheme.gradientStart,
                        CustomTheme.gradientEnd,
                      ],
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                    )
                  : null,
              color: isPopular ? null : theme.colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(16),
              border: isPopular
                  ? null
                  : Border.all(
                      color: theme.colorScheme.outline.withAlpha((0.2 * 255).round()),
                    ),
            ),
            child: ElevatedButton(
              onPressed: _isPurchasing ? null : () => _purchasePackage(package),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.transparent,
                shadowColor: Colors.transparent,
                padding: const EdgeInsets.all(20),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
              child: _isPurchasing
                  ? const CircularProgressIndicator()
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              package.storeProduct.title,
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                                color: isPopular ? Colors.white : null,
                              ),
                            ),
                            Text(
                              package.storeProduct.description,
                              style: TextStyle(
                                fontSize: 14,
                                color: isPopular 
                                    ? Colors.white.withAlpha((0.8 * 255).round())
                                    : theme.textTheme.bodyMedium?.color?.withAlpha((0.7 * 255).round()),
                              ),
                            ),
                          ],
                        ),
                        Text(
                          package.storeProduct.priceString,
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 18,
                            color: isPopular ? Colors.white : theme.colorScheme.primary,
                          ),
                        ),
                      ],
                    ),
            ),
          ),
          if (isPopular)
            Positioned(
              top: -8,
              right: 16,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                decoration: BoxDecoration(
                  color: CustomTheme.recipeOrange,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Text(
                  'POPULAR',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
