import 'package:flutter/material.dart';
import 'package:ailacarte/theme/custom_theme.dart';
import 'package:ailacarte/widgets/themed_background.dart';

class GroceryListScreen extends StatefulWidget {
  const GroceryListScreen({super.key});

  @override
  State<GroceryListScreen> createState() => _GroceryListScreenState();
}

class _GroceryListScreenState extends State<GroceryListScreen> {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _textController = TextEditingController();
  bool _showTitle = false;

  // Mock data for grocery items
  final List<GroceryItem> _groceryItems = [
    GroceryItem(
      id: '1',
      name: 'Tomatoes',
      quantity: '2 lbs',
      category: 'Vegetables',
      isCompleted: false,
    ),
    GroceryItem(
      id: '2',
      name: 'Chicken Breast',
      quantity: '1 lb',
      category: 'Meat',
      isCompleted: true,
    ),
    GroceryItem(
      id: '3',
      name: 'Pasta',
      quantity: '1 box',
      category: 'Pantry',
      isCompleted: false,
    ),
    GroceryItem(
      id: '4',
      name: 'Milk',
      quantity: '1 gallon',
      category: 'Dairy',
      isCompleted: false,
    ),
    GroceryItem(
      id: '5',
      name: 'Bread',
      quantity: '1 loaf',
      category: 'Bakery',
      isCompleted: true,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _textController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.hasClients) {
      final showTitle = _scrollController.offset > 30;
      if (showTitle != _showTitle) {
        setState(() {
          _showTitle = showTitle;
        });
      }
    }
  }

  void _toggleItem(String id) {
    setState(() {
      final index = _groceryItems.indexWhere((item) => item.id == id);
      if (index != -1) {
        _groceryItems[index] = _groceryItems[index].copyWith(
          isCompleted: !_groceryItems[index].isCompleted,
        );
      }
    });
  }

  void _addItem() {
    if (_textController.text.trim().isNotEmpty) {
      setState(() {
        _groceryItems.add(
          GroceryItem(
            id: DateTime.now().millisecondsSinceEpoch.toString(),
            name: _textController.text.trim(),
            quantity: '1',
            category: 'Other',
            isCompleted: false,
          ),
        );
      });
      _textController.clear();
    }
  }

  void _removeItem(String id) {
    setState(() {
      _groceryItems.removeWhere((item) => item.id == id);
    });
  }

  int get _completedCount => _groceryItems.where((item) => item.isCompleted).length;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return ThemedBackground(
      showGradient: false,
      child: CustomScrollView(
        controller: _scrollController,
        slivers: [
          // App Bar
          SliverAppBar(
            expandedHeight: 120,
            floating: false,
            pinned: true,
            backgroundColor: Colors.transparent,
            elevation: 0,
            flexibleSpace: FlexibleSpaceBar(
              title: _showTitle
                  ? Text(
                      'Grocery List',
                      style: TextStyle(
                        color: theme.colorScheme.onSurface,
                        fontWeight: FontWeight.bold,
                      ),
                    )
                  : null,
              background: Container(
                padding: const EdgeInsets.fromLTRB(24, 60, 24, 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Text(
                      'Grocery List',
                      style: theme.textTheme.headlineLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.primary,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '$_completedCount of ${_groceryItems.length} items completed',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.textTheme.bodyMedium?.color?.withOpacity(0.7),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            actions: [
              IconButton(
                onPressed: () {
                  // TODO: Implement clear completed
                  setState(() {
                    _groceryItems.removeWhere((item) => item.isCompleted);
                  });
                },
                icon: const Icon(Icons.clear_all),
              ),
              IconButton(
                onPressed: () {
                  // TODO: Implement share list
                },
                icon: const Icon(Icons.share),
              ),
            ],
          ),

          // Add item section
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.fromLTRB(24, 16, 24, 16),
              child: Card(
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: _textController,
                          decoration: const InputDecoration(
                            hintText: 'Add new item...',
                            border: InputBorder.none,
                            contentPadding: EdgeInsets.zero,
                          ),
                          onSubmitted: (_) => _addItem(),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              CustomTheme.gradientStart,
                              CustomTheme.gradientEnd,
                            ],
                            begin: Alignment.centerLeft,
                            end: Alignment.centerRight,
                          ),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: IconButton(
                          onPressed: _addItem,
                          icon: const Icon(Icons.add, color: Colors.white),
                          style: IconButton.styleFrom(
                            backgroundColor: Colors.transparent,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),

          // Progress indicator
          if (_groceryItems.isNotEmpty)
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.fromLTRB(24, 0, 24, 16),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Progress',
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          '${((_completedCount / _groceryItems.length) * 100).round()}%',
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.primary,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    LinearProgressIndicator(
                      value: _groceryItems.isEmpty ? 0 : _completedCount / _groceryItems.length,
                      backgroundColor: theme.colorScheme.surfaceVariant,
                      valueColor: AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ],
                ),
              ),
            ),

          // Grocery List
          SliverPadding(
            padding: const EdgeInsets.fromLTRB(24, 16, 24, 100),
            sliver: _groceryItems.isEmpty
                ? SliverFillRemaining(
                    child: _buildEmptyState(context),
                  )
                : SliverList(
                    delegate: SliverChildBuilderDelegate(
                      (context, index) {
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 8),
                          child: _buildGroceryItem(_groceryItems[index]),
                        );
                      },
                      childCount: _groceryItems.length,
                    ),
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final theme = Theme.of(context);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(60),
            ),
            child: Icon(
              Icons.shopping_cart,
              size: 60,
              color: theme.colorScheme.primary,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'Your List is Empty',
            style: theme.textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.primary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Add items to start building your grocery list',
            style: theme.textTheme.bodyLarge?.copyWith(
              color: theme.textTheme.bodyLarge?.color?.withOpacity(0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildGroceryItem(GroceryItem item) {
    final theme = Theme.of(context);

    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListTile(
        leading: Checkbox(
          value: item.isCompleted,
          onChanged: (_) => _toggleItem(item.id),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(4),
          ),
        ),
        title: Text(
          item.name,
          style: TextStyle(
            decoration: item.isCompleted ? TextDecoration.lineThrough : null,
            color: item.isCompleted
                ? theme.textTheme.bodyMedium?.color?.withOpacity(0.5)
                : null,
            fontWeight: FontWeight.w500,
          ),
        ),
        subtitle: Row(
          children: [
            Text(
              item.quantity,
              style: TextStyle(
                color: item.isCompleted
                    ? theme.textTheme.bodySmall?.color?.withOpacity(0.5)
                    : theme.colorScheme.primary,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: _getCategoryColor(item.category).withOpacity(0.1),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Text(
                item.category,
                style: TextStyle(
                  fontSize: 10,
                  color: _getCategoryColor(item.category),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        trailing: IconButton(
          onPressed: () => _removeItem(item.id),
          icon: const Icon(Icons.delete_outline),
          color: Colors.red.withOpacity(0.7),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      ),
    );
  }

  Color _getCategoryColor(String category) {
    switch (category.toLowerCase()) {
      case 'vegetables':
        return CustomTheme.recipeGreen;
      case 'meat':
        return CustomTheme.recipeRed;
      case 'dairy':
        return Colors.blue;
      case 'pantry':
        return CustomTheme.recipeYellow;
      case 'bakery':
        return CustomTheme.recipeOrange;
      default:
        return CustomTheme.primaryColor;
    }
  }
}

class GroceryItem {
  final String id;
  final String name;
  final String quantity;
  final String category;
  final bool isCompleted;

  GroceryItem({
    required this.id,
    required this.name,
    required this.quantity,
    required this.category,
    required this.isCompleted,
  });

  GroceryItem copyWith({
    String? id,
    String? name,
    String? quantity,
    String? category,
    bool? isCompleted,
  }) {
    return GroceryItem(
      id: id ?? this.id,
      name: name ?? this.name,
      quantity: quantity ?? this.quantity,
      category: category ?? this.category,
      isCompleted: isCompleted ?? this.isCompleted,
    );
  }
}
