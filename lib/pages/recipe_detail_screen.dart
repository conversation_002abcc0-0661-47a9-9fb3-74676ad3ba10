import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:ailacarte/database/database.dart';
import 'package:ailacarte/services/data_sync_service.dart';
import 'package:ailacarte/widgets/themed_background.dart';
import 'package:ailacarte/di/service_locator.dart';
import 'package:ailacarte/models/recipe_enums.dart';

class RecipeDetailScreen extends StatefulWidget {
  final String recipeId;

  const RecipeDetailScreen({
    super.key,
    required this.recipeId,
  });

  @override
  State<RecipeDetailScreen> createState() => _RecipeDetailScreenState();
}

class _RecipeDetailScreenState extends State<RecipeDetailScreen> {
  final AppDatabase _database = getIt<AppDatabase>();
  Recipe? _recipe;
  List<Ingredient> _ingredients = [];
  List<String> _imageUrls = [];
  bool _isLoading = true;
  bool _isFavorite = false;
  int _currentImageIndex = 0;

  @override
  void initState() {
    super.initState();
    _loadRecipeData();
  }

  Future<void> _loadRecipeData() async {
    try {
      final recipe = await _database.getRecipeById(widget.recipeId);
      final ingredients = await _database.getRecipeIngredients(widget.recipeId);
      
      if (recipe != null) {
        setState(() {
          _recipe = recipe;
          _ingredients = ingredients;
          _isFavorite = recipe.isFavorite;
          _imageUrls = recipe.imageUrls != null 
              ? List<String>.from(jsonDecode(recipe.imageUrls!))
              : [];
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading recipe: $e')),
        );
      }
    }
  }

  Future<void> _toggleFavorite() async {
    if (_recipe != null) {
      final newFavoriteStatus = await _database.toggleRecipeFavorite(_recipe!.id);
      setState(() {
        _isFavorite = newFavoriteStatus;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;

    if (_isLoading) {
      return Scaffold(
        body: ThemedBackground(
          child: Center(
            child: CircularProgressIndicator(
              color: theme.colorScheme.primary,
            ),
          ),
        ),
      );
    }

    if (_recipe == null) {
      return Scaffold(
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
        ),
        body: ThemedBackground(
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: theme.colorScheme.error,
                ),
                const SizedBox(height: 16),
                Text(
                  l10n.recipeNotFound,
                  style: theme.textTheme.headlineSmall,
                ),
              ],
            ),
          ),
        ),
      );
    }

    return Scaffold(
      body: ThemedBackground(
        showGradient: false,
        child: CustomScrollView(
          slivers: [
            // Image carousel with recipe title overlay
            SliverAppBar(
              expandedHeight: 300,
              pinned: true,
              backgroundColor: Colors.transparent,
              elevation: 0,
              leading: Container(
                margin: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.black.withAlpha((0.3 * 255).round()),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: IconButton(
                  icon: const Icon(Icons.arrow_back, color: Colors.white),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ),
              actions: [
                Container(
                  margin: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.black.withAlpha((0.3 * 255).round()),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: IconButton(
                    icon: Icon(
                      _isFavorite ? Icons.favorite : Icons.favorite_border,
                      color: _isFavorite ? Colors.red : Colors.white,
                    ),
                    onPressed: _toggleFavorite,
                  ),
                ),
              ],
              flexibleSpace: FlexibleSpaceBar(
                background: _buildImageCarousel(),
                title: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.bottomCenter,
                      end: Alignment.topCenter,
                      colors: [
                        Colors.black.withAlpha((0.8 * 255).round()),
                        Colors.transparent,
                      ],
                    ),
                  ),
                  child: Text(
                    _recipe!.title,
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                titlePadding: EdgeInsets.zero,
              ),
            ),

            // Recipe content
            SliverPadding(
              padding: const EdgeInsets.all(24),
              sliver: SliverList(
                delegate: SliverChildListDelegate([
                  _buildMetadataSection(theme, l10n),
                  const SizedBox(height: 24),
                  _buildIngredientsSection(theme, l10n),
                  const SizedBox(height: 24),
                  _buildInstructionsSection(theme, l10n),
                  const SizedBox(height: 24),
                  _buildNutritionSection(theme, l10n),
                  const SizedBox(height: 100), // Bottom padding for FAB
                ]),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () async {
          try {
            final dataSyncService = getIt<DataSyncService>();
            await dataSyncService.generateGroceryItemsFromRecipe(_recipe!.id);
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text(l10n.addedIngredientsToGroceryList)),
              );
            }
          } catch (e) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Error: $e')),
              );
            }
          }
        },
        icon: const Icon(Icons.add_shopping_cart),
        label: Text(l10n.addToGroceryList),
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: Colors.white,
      ),
    );
  }

  Widget _buildImageCarousel() {
    if (_imageUrls.isEmpty) {
      return Container(
        color: Colors.grey[300],
        child: const Center(
          child: Icon(
            Icons.restaurant,
            size: 64,
            color: Colors.grey,
          ),
        ),
      );
    }

    return Stack(
      children: [
        CarouselSlider(
          options: CarouselOptions(
            height: double.infinity,
            viewportFraction: 1.0,
            enableInfiniteScroll: _imageUrls.length > 1,
            onPageChanged: (index, reason) {
              setState(() {
                _currentImageIndex = index;
              });
            },
          ),
          items: _imageUrls.map((url) {
            return Image.network(
              url,
              fit: BoxFit.cover,
              width: double.infinity,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  color: Colors.grey[300],
                  child: const Center(
                    child: Icon(
                      Icons.broken_image,
                      size: 64,
                      color: Colors.grey,
                    ),
                  ),
                );
              },
            );
          }).toList(),
        ),
        if (_imageUrls.length > 1)
          Positioned(
            bottom: 16,
            left: 0,
            right: 0,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: _imageUrls.asMap().entries.map((entry) {
                return Container(
                  width: 8,
                  height: 8,
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: _currentImageIndex == entry.key
                        ? Colors.white
                        : Colors.white.withAlpha((0.4 * 255).round()),
                  ),
                );
              }).toList(),
            ),
          ),
      ],
    );
  }

  Widget _buildMetadataSection(ThemeData theme, AppLocalizations l10n) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.recipeDetails,
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildMetadataItem(
                    icon: Icons.access_time,
                    label: l10n.cookTime,
                    value: '${_recipe!.cookingTimeMinutes} min',
                    theme: theme,
                  ),
                ),
                Expanded(
                  child: _buildMetadataItem(
                    icon: Icons.people,
                    label: l10n.servings,
                    value: '${_recipe!.servings}',
                    theme: theme,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildMetadataItem(
                    icon: Icons.signal_cellular_alt,
                    label: l10n.difficulty,
                    value: difficultyLabel(difficultyFromInt(_recipe!.difficulty)),
                    theme: theme,
                  ),
                ),
                Expanded(
                  child: _buildMetadataItem(
                    icon: Icons.category,
                    label: l10n.category,
                    value: categoryLabel(categoryFromInt(_recipe!.category)),
                    theme: theme,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMetadataItem({
    required IconData icon,
    required String label,
    required String value,
    required ThemeData theme,
  }) {
    return Column(
      children: [
        Icon(icon, color: theme.colorScheme.primary),
        const SizedBox(height: 4),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.textTheme.bodySmall?.color?.withAlpha((0.7 * 255).round()),
          ),
        ),
        Text(
          value,
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildIngredientsSection(ThemeData theme, AppLocalizations l10n) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.ingredients,
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: 16),
            ..._ingredients.map((ingredient) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                children: [
                  Container(
                    width: 6,
                    height: 6,
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      '${ingredient.amount} ${ingredient.unit} ${ingredient.name}',
                      style: theme.textTheme.bodyMedium,
                    ),
                  ),
                ],
              ),
            )),
          ],
        ),
      ),
    );
  }

  Widget _buildInstructionsSection(ThemeData theme, AppLocalizations l10n) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.instructions,
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: 16),
            MarkdownBody(
              data: _recipe!.instructions,
              styleSheet: MarkdownStyleSheet(
                p: theme.textTheme.bodyMedium,
                h1: theme.textTheme.headlineSmall?.copyWith(
                  color: theme.colorScheme.primary,
                  fontWeight: FontWeight.bold,
                ),
                h2: theme.textTheme.titleLarge?.copyWith(
                  color: theme.colorScheme.primary,
                  fontWeight: FontWeight.bold,
                ),
                h3: theme.textTheme.titleMedium?.copyWith(
                  color: theme.colorScheme.primary,
                  fontWeight: FontWeight.bold,
                ),
                listBullet: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.primary,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNutritionSection(ThemeData theme, AppLocalizations l10n) {
    if (_recipe!.calories == null) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.nutritionInformation,
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 16,
              runSpacing: 12,
              children: [
                if (_recipe!.calories != null)
                  _buildNutritionItem('Calories', '${_recipe!.calories}', 'kcal', theme),
                if (_recipe!.protein != null)
                  _buildNutritionItem('Protein', '${_recipe!.protein?.toStringAsFixed(1)}', 'g', theme),
                if (_recipe!.carbs != null)
                  _buildNutritionItem('Carbs', '${_recipe!.carbs?.toStringAsFixed(1)}', 'g', theme),
                if (_recipe!.fat != null)
                  _buildNutritionItem('Fat', '${_recipe!.fat?.toStringAsFixed(1)}', 'g', theme),
                if (_recipe!.fiber != null)
                  _buildNutritionItem('Fiber', '${_recipe!.fiber?.toStringAsFixed(1)}', 'g', theme),
                if (_recipe!.sugar != null)
                  _buildNutritionItem('Sugar', '${_recipe!.sugar?.toStringAsFixed(1)}', 'g', theme),
                if (_recipe!.sodium != null)
                  _buildNutritionItem('Sodium', '${_recipe!.sodium?.toStringAsFixed(0)}', 'mg', theme),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNutritionItem(String label, String value, String unit, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: theme.colorScheme.primary.withAlpha((0.1 * 255).round()),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            '$value$unit',
            style: theme.textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.primary,
            ),
          ),
          Text(
            label,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.textTheme.bodySmall?.color?.withAlpha((0.7 * 255).round()),
            ),
          ),
        ],
      ),
    );
  }
}
