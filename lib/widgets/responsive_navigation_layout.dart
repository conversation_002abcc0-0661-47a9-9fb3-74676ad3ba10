import 'package:flutter/material.dart';
import 'package:ailacarte/widgets/custom_navigation_bar.dart';

class ResponsiveNavigationLayout extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;
  final List<Widget> children;

  const ResponsiveNavigationLayout({
    super.key,
    required this.currentIndex,
    required this.onTap,
    required this.children,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: IndexedStack(
        index: currentIndex,
        children: children,
      ),
      extendBody: true,
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
        child: CustomNavigationBar(
          currentIndex: currentIndex,
          onTap: onTap,
        ),
      ),
    );
  }
}
