import 'package:flutter/material.dart';
import 'package:ailacarte/theme/custom_theme.dart';

class ThemedBackground extends StatelessWidget {
  final Widget child;
  final bool showGradient;

  const ThemedBackground({
    super.key,
    required this.child,
    this.showGradient = true,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: showGradient
            ? BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    CustomTheme.gradientStart.withOpacity(0.1),
                    CustomTheme.gradientEnd.withOpacity(0.1),
                  ],
                ),
              )
            : null,
        child: Safe<PERSON>rea(child: child),
      ),
    );
  }
}
