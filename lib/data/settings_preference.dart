import 'package:shared_preferences/shared_preferences.dart';

class SettingsPreferences {
  final SharedPreferences _prefs;

  SettingsPreferences(this._prefs);

  // Keys
  static const String _keyOnboardingCompleted = 'onboarding_completed';
  static const String _keyThemeMode = 'theme_mode';
  static const String _keyLanguageCode = 'language_code';
  static const String _keyNotificationsEnabled = 'notifications_enabled';
  static const String _keyFirstLaunch = 'first_launch';

  // Onboarding
  bool get isOnboardingCompleted => _prefs.getBool(_keyOnboardingCompleted) ?? false;
  Future<void> setOnboardingCompleted(bool completed) async {
    await _prefs.setBool(_keyOnboardingCompleted, completed);
  }

  // Theme
  String get themeMode => _prefs.getString(_keyThemeMode) ?? 'system';
  Future<void> setThemeMode(String mode) async {
    await _prefs.setString(_keyThemeMode, mode);
  }

  // Language
  String get languageCode => _prefs.getString(_keyLanguageCode) ?? 'en';
  Future<void> setLanguageCode(String code) async {
    await _prefs.setString(_keyLanguageCode, code);
  }

  // Notifications
  bool get notificationsEnabled => _prefs.getBool(_keyNotificationsEnabled) ?? false;
  Future<void> setNotificationsEnabled(bool enabled) async {
    await _prefs.setBool(_keyNotificationsEnabled, enabled);
  }

  // First launch
  bool get isFirstLaunch => _prefs.getBool(_keyFirstLaunch) ?? true;
  Future<void> setFirstLaunch(bool isFirst) async {
    await _prefs.setBool(_keyFirstLaunch, isFirst);
  }

  // Clear all settings
  Future<void> clearAll() async {
    await _prefs.clear();
  }
}
