{"name": "firebase_crashlytics", "version": "4.3.9", "summary": "Flutter plugin for Firebase Crashlytics. It reports uncaught errors to the Firebase console.", "description": "Flutter plugin for Firebase Crashlytics. It reports uncaught errors to the Firebase console.", "homepage": "https://firebase.google.com/docs/crashlytics", "license": {"file": "../LICENSE"}, "authors": "The Chromium Authors", "source": {"path": "."}, "source_files": "firebase_crashlytics/Sources/firebase_crashlytics/**/*.{h,m}", "public_header_files": "firebase_crashlytics/Sources/firebase_crashlytics/include/*.h", "platforms": {"ios": "13.0"}, "dependencies": {"Flutter": [], "firebase_core": [], "Firebase/Crashlytics": ["11.15.0"]}, "static_framework": true, "pod_target_xcconfig": {"GCC_PREPROCESSOR_DEFINITIONS": "LIBRARY_VERSION=\\\"4.3.9\\\" LIBRARY_NAME=\\\"flutter-fire-cls\\\"", "DEFINES_MODULE": "YES"}, "user_target_xcconfig": {"DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym"}}