{"name": "permission_handler_apple", "version": "9.3.0", "summary": "Permission plugin for Flutter.", "description": "Permission plugin for Flutter. This plugin provides a cross-platform (iOS, Android) API to request and check permissions.", "homepage": "https://github.com/baseflowit/flutter-permission-handler", "license": {"file": "../LICENSE"}, "authors": {"Baseflow": "<EMAIL>"}, "source": {"path": "."}, "source_files": "Classes/**/*", "public_header_files": "Classes/**/*.h", "dependencies": {"Flutter": []}, "platforms": {"ios": "8.0"}, "static_framework": true, "resource_bundles": {"permission_handler_apple_privacy": ["Resources/PrivacyInfo.xcprivacy"]}}