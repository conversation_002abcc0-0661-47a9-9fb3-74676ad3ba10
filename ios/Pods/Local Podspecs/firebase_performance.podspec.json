{"name": "firebase_performance", "version": "0.10.1-9", "summary": "Flutter plugin for Google Performance Monitoring for Firebase, an app measurement solution that monitors traces and HTTP/S network requests on Android and iOS.", "description": "Flutter plugin for Google Performance Monitoring for Firebase, an app measurement solution that monitors traces and HTTP/S network requests on Android and iOS.", "homepage": "https://firebase.google.com/docs/perf-mon", "license": {"file": "../LICENSE"}, "authors": "The Chromium Authors", "source": {"path": "."}, "source_files": "firebase_performance/Sources/firebase_performance/**/*.{h,m}", "public_header_files": "firebase_performance/Sources/firebase_performance/include/*.h", "dependencies": {"Flutter": [], "firebase_core": [], "Firebase/Performance": ["11.15.0"]}, "platforms": {"ios": "13.0"}, "static_framework": true, "pod_target_xcconfig": {"GCC_PREPROCESSOR_DEFINITIONS": "LIBRARY_VERSION=\\\"0.10.1-9\\\" LIBRARY_NAME=\\\"flutter-fire-perf\\\"", "DEFINES_MODULE": "YES"}}