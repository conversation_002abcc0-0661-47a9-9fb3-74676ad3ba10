{"name": "firebase_core", "version": "3.15.1", "summary": "Flutter plugin for Firebase Core, enabling connecting to multiple Firebase apps.", "description": "Flutter plugin for Firebase Core, enabling connecting to multiple Firebase apps.", "homepage": "https://firebase.google.com/docs/flutter/setup", "license": {"file": "../LICENSE"}, "authors": "The Chromium Authors", "source": {"path": "."}, "source_files": "firebase_core/Sources/firebase_core/**/*.{h,m}", "public_header_files": "firebase_core/Sources/firebase_core/include/**/*.h", "platforms": {"ios": "13.0"}, "dependencies": {"Flutter": [], "Firebase/CoreOnly": ["11.15.0"]}, "static_framework": true, "pod_target_xcconfig": {"GCC_PREPROCESSOR_DEFINITIONS": "LIBRARY_VERSION=\\\"3.15.1\\\" LIBRARY_NAME=\\\"flutter-fire-core\\\"", "DEFINES_MODULE": "YES"}}