{"name": "google_sign_in_ios", "version": "0.0.1", "summary": "Google Sign-In plugin for Flutter", "description": "Enables Google Sign-In in Flutter apps.", "homepage": "https://github.com/flutter/packages/tree/main/packages/google_sign_in", "license": {"type": "BSD", "file": "../LICENSE"}, "authors": {"Flutter Team": "<EMAIL>"}, "source": {"http": "https://github.com/flutter/packages/tree/main/packages/google_sign_in/google_sign_in_ios"}, "source_files": "google_sign_in_ios/Sources/google_sign_in_ios/**/*.{h,m}", "public_header_files": "google_sign_in_ios/Sources/google_sign_in_ios/include/**/*.h", "module_map": "google_sign_in_ios/Sources/google_sign_in_ios/include/FLTGoogleSignInPlugin.modulemap", "dependencies": {"AppAuth": [">= 1.7.4"], "GTMSessionFetcher": [">= 3.4.0"], "GoogleSignIn": ["~> 8.0"]}, "static_framework": true, "ios": {"dependencies": {"Flutter": []}}, "osx": {"dependencies": {"FlutterMacOS": []}}, "platforms": {"ios": "12.0", "osx": "10.15"}, "pod_target_xcconfig": {"DEFINES_MODULE": "YES"}, "swift_versions": "5.0", "resource_bundles": {"google_sign_in_ios_privacy": ["google_sign_in_ios/Sources/google_sign_in_ios/Resources/PrivacyInfo.xcprivacy"]}, "swift_version": "5.0"}