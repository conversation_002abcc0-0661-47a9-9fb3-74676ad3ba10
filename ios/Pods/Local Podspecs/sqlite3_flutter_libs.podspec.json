{"name": "sqlite3_flutter_libs", "version": "0.0.1", "summary": "A new flutter plugin project.", "description": "A new flutter plugin project.", "homepage": "http://example.com", "license": {"file": "../LICENSE"}, "authors": {"Your Company": "<EMAIL>"}, "source": {"path": "."}, "source_files": "sqlite3_flutter_libs/Sources/sqlite3_flutter_libs/**/*.swift", "ios": {"dependencies": {"Flutter": []}, "xcconfig": {"LIBRARY_SEARCH_PATHS": "$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)/ $(SDKROOT)/usr/lib/swift", "LD_RUNPATH_SEARCH_PATHS": "/usr/lib/swift"}}, "osx": {"dependencies": {"FlutterMacOS": []}}, "platforms": {"ios": "12.0", "osx": "10.14"}, "swift_versions": "5.0", "dependencies": {"sqlite3": ["~> 3.50.1"], "sqlite3/fts5": [], "sqlite3/perf-threadsafe": [], "sqlite3/rtree": [], "sqlite3/dbstatvtab": [], "sqlite3/math": []}, "swift_version": "5.0"}