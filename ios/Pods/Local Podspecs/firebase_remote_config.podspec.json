{"name": "firebase_remote_config", "version": "5.4.7", "summary": "Flutter plugin for Firebase Remote Config. Update your application look and feel and behavior without re-releasing.", "description": "Flutter plugin for Firebase Remote Config. Update your application look and feel and behavior without re-releasing.", "homepage": "https://firebase.google.com/docs/remote-config", "license": {"file": "../LICENSE"}, "authors": "The Chromium Authors", "source": {"path": "."}, "source_files": "firebase_remote_config/Sources/firebase_remote_config/**/*.{h,m}", "public_header_files": "firebase_remote_config/Sources/firebase_remote_config/include/*.h", "platforms": {"ios": "13.0"}, "dependencies": {"Flutter": [], "firebase_core": [], "Firebase/RemoteConfig": ["11.15.0"]}, "static_framework": true, "pod_target_xcconfig": {"GCC_PREPROCESSOR_DEFINITIONS": "LIBRARY_VERSION=\\\"5.4.7\\\" LIBRARY_NAME=\\\"flutter-fire-rc\\\"", "DEFINES_MODULE": "YES"}}