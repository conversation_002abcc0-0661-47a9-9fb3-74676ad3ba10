{"name": "shared_preferences_foundation", "version": "0.0.1", "summary": "iOS and macOS implementation of the shared_preferences plugin.", "description": "Wraps NSUserDefaults, providing a persistent store for simple key-value pairs.", "homepage": "https://github.com/flutter/packages/tree/main/packages/shared_preferences/shared_preferences_foundation", "license": {"type": "BSD", "file": "../LICENSE"}, "authors": {"Flutter Team": "<EMAIL>"}, "source": {"http": "https://github.com/flutter/packages/tree/main/packages/shared_preferences/shared_preferences_foundation"}, "source_files": "shared_preferences_foundation/Sources/shared_preferences_foundation/**/*.swift", "ios": {"dependencies": {"Flutter": []}}, "osx": {"dependencies": {"FlutterMacOS": []}}, "platforms": {"ios": "12.0", "osx": "10.14"}, "pod_target_xcconfig": {"DEFINES_MODULE": "YES"}, "xcconfig": {"LIBRARY_SEARCH_PATHS": "$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)/ $(SDKROOT)/usr/lib/swift", "LD_RUNPATH_SEARCH_PATHS": "/usr/lib/swift"}, "swift_versions": "5.0", "resource_bundles": {"shared_preferences_foundation_privacy": ["shared_preferences_foundation/Sources/shared_preferences_foundation/Resources/PrivacyInfo.xcprivacy"]}, "swift_version": "5.0"}