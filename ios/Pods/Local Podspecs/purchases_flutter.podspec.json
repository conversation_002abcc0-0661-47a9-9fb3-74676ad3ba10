{"name": "purchases_flutter", "version": "8.10.5", "summary": "Cross-platform subscriptions framework for Flutter.", "description": "Client for the RevenueCat subscription and purchase tracking system, making implementing in-app subscriptions in Flutter easy - receipt validation and status tracking included!", "homepage": "http://revenuecat.com", "license": {"file": "../LICENSE"}, "authors": {"RevenueCat": "<EMAIL>"}, "source": {"path": "."}, "source_files": "Classes/**/*", "public_header_files": "Classes/**/*.h", "dependencies": {"Flutter": [], "PurchasesHybridCommon": ["14.0.2"]}, "platforms": {"ios": "13.0"}, "swift_versions": "5.0", "pod_target_xcconfig": {"DEFINES_MODULE": "YES", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "i386", "OTHER_LDFLAGS": "-framework RevenueCat"}, "swift_version": "5.0"}