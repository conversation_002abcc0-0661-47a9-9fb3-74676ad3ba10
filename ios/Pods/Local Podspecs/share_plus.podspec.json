{"name": "share_plus", "version": "0.0.1", "summary": "Flutter Share", "description": "A Flutter plugin to share content from your Flutter app via the platform's share dialog.\nDownloaded by pub (not CocoaPods).", "homepage": "https://github.com/fluttercommunity/plus_plugins", "license": {"type": "BSD", "file": "../LICENSE"}, "authors": {"Flutter Community Team": "<EMAIL>"}, "source": {"http": "https://github.com/fluttercommunity/plus_plugins/tree/main/packages/share_plus/share_plus"}, "documentation_url": "https://pub.dev/packages/share_plus", "source_files": "Classes/**/*", "public_header_files": "Classes/**/*.h", "dependencies": {"Flutter": []}, "ios": {"weak_frameworks": "LinkPresentation"}, "platforms": {"ios": "11.0"}, "pod_target_xcconfig": {"DEFINES_MODULE": "YES"}}