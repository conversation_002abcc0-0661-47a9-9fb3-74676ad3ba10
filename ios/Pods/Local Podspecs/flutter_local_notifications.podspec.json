{"name": "flutter_local_notifications", "version": "0.0.1", "summary": "Flutter plugin for displaying local notifications.", "description": "Flutter plugin for displaying local notifications.", "homepage": "https://github.com/MaikuB/flutter_local_notifications/tree/master/flutter_local_notifications", "license": {"type": "BSD", "file": "../LICENSE"}, "authors": {"Michael Bui": "micha<PERSON>@dexterx.dev"}, "source": {"path": "."}, "source_files": "Classes/**/*", "public_header_files": "Classes/**/*.h", "dependencies": {"Flutter": []}, "resource_bundles": {"flutter_local_notifications_privacy": ["Resources/PrivacyInfo.xcprivacy"]}, "platforms": {"ios": "8.0"}}