{"name": "sign_in_with_apple", "version": "0.0.1", "summary": "Flutter plugin for handling Sign in with Apple", "description": "Flutter bridge to initiate Sign in with Apple (currently iOS only). Includes support for keychain entries as well as sign in with an Apple ID.", "homepage": "https://github.com/aboutyou/dart_packages/tree/master/packages/sign_in_with_apple", "license": {"file": "../LICENSE"}, "authors": {"Timm Preetz": "<EMAIL>"}, "source": {"path": "."}, "source_files": "Classes/**/*", "dependencies": {"Flutter": []}, "platforms": {"ios": "9.0"}, "pod_target_xcconfig": {"DEFINES_MODULE": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "x86_64"}, "swift_versions": "5.0", "swift_version": "5.0"}