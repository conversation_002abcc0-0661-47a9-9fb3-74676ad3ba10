name: a<PERSON><PERSON>te
description: "AI La Carte - Your AI-powered recipe collection app."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  intl: ^0.20.2
  flutter_localizations:
    sdk: flutter
  bloc: ^8.1.4
  flutter_bloc: ^8.1.4
  uuid: ^4.3.3
  equatable: ^2.0.5
  shared_preferences: ^2.2.2
  figma_squircle: ^0.5.3
  rxdart: ^0.27.7
  path: ^1.8.2
  supabase_flutter: ^2.3.4

  # Database
  drift: ^2.16.0
  sqlite3_flutter_libs: ^0.5.20
  flutter_svg: ^2.0.10+1
  google_sign_in: ^6.3.0
  fl_chart: ^1.0.0
  dio: ^5.4.0

  # Firebase
  firebase_core: ^3.13.0
  firebase_analytics: ^11.4.1
  firebase_analytics_web: ^0.5.3+1
  firebase_crashlytics: ^4.2.0
  firebase_performance: ^0.10.0+1
  firebase_remote_config: ^5.3.0

  # Dependency injection
  get_it: ^7.6.7

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8

  # File handling
  file_picker: ^10.1.9
  open_file: ^3.3.2
  path_provider: ^2.1.2

  # Notifications
  flutter_local_notifications: ^16.3.2
  timezone: ^0.9.2

  # About screen
  package_info_plus: ^8.3.0
  url_launcher: ^6.2.5
  share_plus: ^7.2.2
  font_awesome_flutter: ^10.7.0

  # Image handling
  image_picker: ^1.1.2
  image: ^4.5.2

  # HTTP requests
  http: ^1.2.1

  # Network connectivity
  connectivity_plus: ^5.0.2
  # RevenueCat for in-app purchases
  purchases_flutter: ^8.10.2
  purchases_ui_flutter: ^8.10.2

  # Apple Sign-In
  sign_in_with_apple: ^7.0.1
  crypto: ^3.0.3

  # Routing
  go_router: ^15.1.2

  # Audio-related dependencies
  permission_handler: ^11.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  build_runner: ^2.4.5
  drift_dev: ^2.16.0
  intl_translation: ^0.20.1
  flutter_launcher_icons: ^0.13.1

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

dependency_overrides:
  intl: ^0.20.2

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  generate: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/
    - assets/icons/
    - assets/images/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
